{"totalFiles": 241, "fixedFiles": 103, "fixedFilesList": ["/Users/<USER>/dev/nextjs-main/src/app/api/gpx/parse/route.ts", "/Users/<USER>/dev/nextjs-main/src/app/api/health/route.ts", "/Users/<USER>/dev/nextjs-main/src/app/layout.tsx", "/Users/<USER>/dev/nextjs-main/src/app/page-client-enhanced.tsx", "/Users/<USER>/dev/nextjs-main/src/app/page.tsx", "/Users/<USER>/dev/nextjs-main/src/app/providers.tsx", "/Users/<USER>/dev/nextjs-main/src/components/List.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/ChartInitializer.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/Charts.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/chart-theme.ts", "/Users/<USER>/dev/nextjs-main/src/components/common/FallbackUI.tsx", "/Users/<USER>/dev/nextjs-main/src/components/common/OfflineDetector.tsx", "/Users/<USER>/dev/nextjs-main/src/components/common/ThemeToggle.tsx", "/Users/<USER>/dev/nextjs-main/src/components/layout/footer.tsx", "/Users/<USER>/dev/nextjs-main/src/components/layout/header.tsx", "/Users/<USER>/dev/nextjs-main/src/components/layout/page-wrapper.tsx", "/Users/<USER>/dev/nextjs-main/src/components/layout/responsive-layout.tsx", "/Users/<USER>/dev/nextjs-main/src/components/providers/chart-provider.tsx", "/Users/<USER>/dev/nextjs-main/src/components/providers/performance-provider.tsx", "/Users/<USER>/dev/nextjs-main/src/components/providers/theme-provider.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/ClientSideTimeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/ModernClientTimeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/ModernTimeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/Timeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/AccessibleIcon.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/CustomLoader.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/EmptyState.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/ErrorCard.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/ErrorMessage.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/FeedbackMessage.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/FormErrorMessage.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/KeyboardFocusOutline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/LazyLoad.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/LoadingCard.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/LoadingOverlay.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/LoadingSkeleton.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/LoadingSpinner.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/ResponsiveContainer.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/ResponsiveGrid.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/SkipToContent.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/TrainLoader.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/accordion.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/alert-card.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/alert.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/badge.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/breadcrumb.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/button.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/dialog.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/enhanced-theme-toggle.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/form.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/input.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/label.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/motion-card.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/motion.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/notification-container.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/notification.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/optimized-list.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/page-transition.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/progress-steps.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/responsive-image.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/scroll-area.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/select.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/sheet.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/simple-notification.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/simple-toast.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/simple-tooltip.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/skeleton.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/slider.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/switch.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/tabs.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/toast.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/toaster.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/tooltip.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/use-toast.ts", "/Users/<USER>/dev/nextjs-main/src/features/export/components/ExportMenu.tsx", "/Users/<USER>/dev/nextjs-main/src/features/export/components/PDFExport.tsx", "/Users/<USER>/dev/nextjs-main/src/features/gpx/components/GPXErrorFallback.tsx", "/Users/<USER>/dev/nextjs-main/src/features/gpx/components/SimpleGPXUploader.tsx", "/Users/<USER>/dev/nextjs-main/src/features/gpx/context/GPXContext.tsx", "/Users/<USER>/dev/nextjs-main/src/features/gpx/utils/forecastGenerator.ts", "/Users/<USER>/dev/nextjs-main/src/features/help/components/UserGuide.tsx", "/Users/<USER>/dev/nextjs-main/src/features/notifications/context/NotificationContext.tsx", "/Users/<USER>/dev/nextjs-main/src/features/notifications/context/NotificationProvider.tsx", "/Users/<USER>/dev/nextjs-main/src/features/notifications/context/SimpleNotificationProvider.tsx", "/Users/<USER>/dev/nextjs-main/src/features/route/components/SimpleRouteControls.tsx", "/Users/<USER>/dev/nextjs-main/src/features/weather/components/SimpleWeatherProviderComparison.tsx", "/Users/<USER>/dev/nextjs-main/src/features/weather/utils/clientWeatherAPI.ts", "/Users/<USER>/dev/nextjs-main/src/features/weather/utils/weatherAPI.ts", "/Users/<USER>/dev/nextjs-main/src/features/weather/utils/weatherProviders.ts", "/Users/<USER>/dev/nextjs-main/src/hooks/useApiRequest.ts", "/Users/<USER>/dev/nextjs-main/src/hooks/useChartTheme.ts", "/Users/<USER>/dev/nextjs-main/src/hooks/useThemeDetection.ts", "/Users/<USER>/dev/nextjs-main/src/lib/chart-defaults.ts", "/Users/<USER>/dev/nextjs-main/src/lib/chart-init.ts", "/Users/<USER>/dev/nextjs-main/src/lib/performance.ts", "/Users/<USER>/dev/nextjs-main/src/lib/utils.ts", "/Users/<USER>/dev/nextjs-main/src/lib/weatherAPI.ts", "/Users/<USER>/dev/nextjs-main/src/lib/weatherProviders.ts", "/Users/<USER>/dev/nextjs-main/src/middleware.ts", "/Users/<USER>/dev/nextjs-main/src/styles/tailwind-utils.ts", "/Users/<USER>/dev/nextjs-main/src/utils/createSafeContext.ts", "/Users/<USER>/dev/nextjs-main/src/utils/inputValidation.ts", "/Users/<USER>/dev/nextjs-main/src/utils/weatherService.ts"]}