{"totalFiles": 114, "fixedFiles": 24, "fixedFilesList": ["/Users/<USER>/dev/nextjs-main/src/components/charts/ElevationChart.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/HumidityChart.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/PrecipitationChart.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/PressureChart.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/TemperatureChart.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/UVIndexChart.tsx", "/Users/<USER>/dev/nextjs-main/src/components/charts/WindChart.tsx", "/Users/<USER>/dev/nextjs-main/src/components/common/OfflineDetector.tsx", "/Users/<USER>/dev/nextjs-main/src/components/common/ThemeToggle.tsx", "/Users/<USER>/dev/nextjs-main/src/components/layout/header.tsx", "/Users/<USER>/dev/nextjs-main/src/components/map/SimpleLeafletMap.tsx", "/Users/<USER>/dev/nextjs-main/src/components/providers/performance-provider.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/ClientSideTimeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/ModernClientTimeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/ModernTimeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/timeline/Timeline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/KeyboardFocusOutline.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/ResponsiveGrid.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/notification.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/simple-notification.tsx", "/Users/<USER>/dev/nextjs-main/src/components/ui/toast.tsx", "/Users/<USER>/dev/nextjs-main/src/features/navigation/components/KeyboardNavigation.tsx", "/Users/<USER>/dev/nextjs-main/src/features/notifications/context/SimpleNotificationProvider.tsx", "/Users/<USER>/dev/nextjs-main/src/features/weather/components/Alerts.tsx"]}