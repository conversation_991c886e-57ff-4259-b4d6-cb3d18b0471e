/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: 'class',
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './frontend/**/*.{js,ts,jsx,tsx,mdx}',
    './backend/**/*.{js,ts,jsx,tsx,mdx}',
    './shared/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  safelist: [
    'hover:text-primary-dark',
    'hover:text-secondary-dark',
    'hover:text-accent-dark',
    'hover:text-destructive-dark',
    'hover:text-success-dark',
    'hover:text-warning-dark',
    'hover:text-info-dark',
    'text-success',
    'text-warning',
    'text-error',
    'text-info',
  ],
  theme: {
    fontFamily: {
      sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'SF Pro Text', 'Segoe UI', 'Roboto', 'Helvetica Neue', 'sans-serif'],
      mono: ['ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', 'monospace'],
    },
    container: {
      center: true,
      padding: {
        DEFAULT: '1rem',
        sm: '1.5rem',
        lg: '2rem'
      },
      screens: {
        sm: '640px',
        md: '768px',
        lg: '1024px',
        xl: '1280px',
        '2xl': '1536px',
      },
    },
    screens: {
      xs: '375px',
      sm: '640px',
      md: '768px',
      lg: '1024px',
      xl: '1280px',
      '2xl': '1536px',
    },
    extend: {
      gridTemplateColumns: {
        'auto-fill-100': 'repeat(auto-fill, minmax(100px, 1fr))',
        'auto-fill-200': 'repeat(auto-fill, minmax(200px, 1fr))',
        'auto-fill-300': 'repeat(auto-fill, minmax(300px, 1fr))',
      },
      colors: {
        theme: {
          text: 'var(--color-text)',
          bg: 'var(--color-bg)',
          card: 'var(--color-card)',
          accent: 'var(--color-accent)',
          primary: 'var(--color-primary)',
          secondary: 'var(--color-secondary)',
          border: 'var(--color-border)',
          shadow: 'var(--color-shadow)',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
          light: 'hsl(var(--primary-light))',
          dark: 'hsl(var(--primary-dark))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
          light: 'hsl(var(--secondary-light))',
          dark: 'hsl(var(--secondary-dark))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
          light: 'hsl(var(--destructive-light))',
          dark: 'hsl(var(--destructive-dark))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
          light: 'hsl(var(--accent-light))',
          dark: 'hsl(var(--accent-dark))',
        },
        success: {
          DEFAULT: 'hsl(var(--success))',
          foreground: 'hsl(var(--success-foreground))',
          light: 'hsl(var(--success-light))',
          dark: 'hsl(var(--success-dark))',
        },
        warning: {
          DEFAULT: 'hsl(var(--warning))',
          foreground: 'hsl(var(--warning-foreground))',
          light: 'hsl(var(--warning-light))',
          dark: 'hsl(var(--warning-dark))',
        },
        info: {
          DEFAULT: 'hsl(var(--info))',
          foreground: 'hsl(var(--info-foreground))',
          light: 'hsl(var(--info-light))',
          dark: 'hsl(var(--info-dark))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        // New theme colors
        theme: {
          bg: 'var(--color-bg)',
          text: 'var(--color-text)',
          accent: 'var(--color-accent)',
          card: 'var(--color-card)',
          shadow: 'var(--color-shadow)',
        },
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
        xl: 'calc(var(--radius) + 4px)',
        '2xl': 'calc(var(--radius) + 8px)',
      },
      spacing: {
        '4.5': '1.125rem',
        '7.5': '1.875rem',
        '9.5': '2.375rem',
        // iOS 19 inspired spacing scale
        '1': '0.25rem',  /* 4px */
        '2': '0.5rem',   /* 8px */
        '3': '0.75rem',  /* 12px */
        '4': '1rem',     /* 16px */
        '5': '1.25rem',  /* 20px */
        '6': '1.5rem',   /* 24px */
        '8': '2rem',     /* 32px */
        '10': '2.5rem',  /* 40px */
        '12': '3rem',    /* 48px */
        '16': '4rem',    /* 64px */
        '20': '5rem',    /* 80px */
        '24': '6rem',    /* 96px */
        // Safe area for iOS
        'safe': 'env(safe-area-inset-bottom, 0px)',
      },
      boxShadow: {
        // iOS 19 inspired shadows
        'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        'DEFAULT': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
        'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        'inner': 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.06)',
        'none': 'none',
        // iOS specific shadows
        'ios-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05), 0 0 1px 0 rgba(0, 0, 0, 0.1)',
        'ios': '0 2px 8px -2px rgba(0, 0, 0, 0.1), 0 0 1px 0 rgba(0, 0, 0, 0.1)',
        'ios-md': '0 4px 12px -4px rgba(0, 0, 0, 0.12), 0 0 1px 0 rgba(0, 0, 0, 0.1)',
        'ios-lg': '0 12px 24px -8px rgba(0, 0, 0, 0.15), 0 0 1px 0 rgba(0, 0, 0, 0.1)',
        'ios-xl': '0 20px 32px -12px rgba(0, 0, 0, 0.2), 0 0 1px 0 rgba(0, 0, 0, 0.1)',
      },
      keyframes: {
        // iOS 19 inspired keyframes with smoother transitions
        'fade-in': {
          '0%': { opacity: 0, transform: 'translateY(8px)' },
          '100%': { opacity: 1, transform: 'translateY(0)' },
        },
        'fade-out': {
          '0%': { opacity: 1, transform: 'translateY(0)' },
          '100%': { opacity: 0, transform: 'translateY(8px)' },
        },
        'slide-in-from-right': {
          '0%': { transform: 'translateX(16px)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        'slide-in-from-left': {
          '0%': { transform: 'translateX(-16px)', opacity: 0 },
          '100%': { transform: 'translateX(0)', opacity: 1 },
        },
        'slide-in-from-top': {
          '0%': { transform: 'translateY(-16px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        'slide-in-from-bottom': {
          '0%': { transform: 'translateY(16px)', opacity: 0 },
          '100%': { transform: 'translateY(0)', opacity: 1 },
        },
        'slide-out-to-right': {
          '0%': { transform: 'translateX(0)', opacity: 1 },
          '100%': { transform: 'translateX(16px)', opacity: 0 },
        },
        'slide-out-to-left': {
          '0%': { transform: 'translateX(0)', opacity: 1 },
          '100%': { transform: 'translateX(-16px)', opacity: 0 },
        },
        'slide-out-to-top': {
          '0%': { transform: 'translateY(0)', opacity: 1 },
          '100%': { transform: 'translateY(-16px)', opacity: 0 },
        },
        'slide-out-to-bottom': {
          '0%': { transform: 'translateY(0)', opacity: 1 },
          '100%': { transform: 'translateY(16px)', opacity: 0 },
        },
        'scale-in': {
          '0%': { transform: 'scale(0.95)', opacity: 0 },
          '100%': { transform: 'scale(1)', opacity: 1 },
        },
        'scale-out': {
          '0%': { transform: 'scale(1)', opacity: 1 },
          '100%': { transform: 'scale(0.95)', opacity: 0 },
        },
        'indeterminate-progress': {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
        'shimmer': {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' },
        },
        'pulse': {
          '0%, 100%': { opacity: 1 },
          '50%': { opacity: 0.6 },
        },
        'spin': {
          '0%': { transform: 'rotate(0deg)' },
          '100%': { transform: 'rotate(360deg)' },
        },
        'wiggle': {
          '0%, 100%': { transform: 'rotate(-2deg)' },
          '50%': { transform: 'rotate(2deg)' },
        },
        'flash': {
          '0%, 50%, 100%': { opacity: 1 },
          '25%, 75%': { opacity: 0.5 },
        },
        'shake': {
          '0%, 100%': { transform: 'translateX(0)' },
          '10%, 30%, 50%, 70%, 90%': { transform: 'translateX(-4px)' },
          '20%, 40%, 60%, 80%': { transform: 'translateX(4px)' },
        },
        'bounce': {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-8px)' },
        },
      },
      animation: {
        // iOS 19 inspired animations with smoother timing
        'fade-in': 'fade-in 250ms cubic-bezier(0, 0, 0.2, 1)',
        'fade-out': 'fade-out 200ms cubic-bezier(0.4, 0, 1, 1)',
        'fade-in-fast': 'fade-in 150ms cubic-bezier(0, 0, 0.2, 1)',
        'fade-out-fast': 'fade-out 150ms cubic-bezier(0.4, 0, 1, 1)',
        'fade-in-slow': 'fade-in 350ms cubic-bezier(0, 0, 0.2, 1)',
        'fade-out-slow': 'fade-out 300ms cubic-bezier(0.4, 0, 1, 1)',

        'slide-in-from-right': 'slide-in-from-right 250ms cubic-bezier(0, 0, 0.2, 1)',
        'slide-in-from-left': 'slide-in-from-left 250ms cubic-bezier(0, 0, 0.2, 1)',
        'slide-in-from-top': 'slide-in-from-top 250ms cubic-bezier(0, 0, 0.2, 1)',
        'slide-in-from-bottom': 'slide-in-from-bottom 250ms cubic-bezier(0, 0, 0.2, 1)',

        'slide-out-to-right': 'slide-out-to-right 200ms cubic-bezier(0.4, 0, 1, 1)',
        'slide-out-to-left': 'slide-out-to-left 200ms cubic-bezier(0.4, 0, 1, 1)',
        'slide-out-to-top': 'slide-out-to-top 200ms cubic-bezier(0.4, 0, 1, 1)',
        'slide-out-to-bottom': 'slide-out-to-bottom 200ms cubic-bezier(0.4, 0, 1, 1)',

        'scale-in': 'scale-in 250ms cubic-bezier(0, 0, 0.2, 1)',
        'scale-out': 'scale-out 200ms cubic-bezier(0.4, 0, 1, 1)',

        'indeterminate-progress': 'indeterminate-progress 1.5s infinite cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'shimmer': 'shimmer 2s infinite linear',
        'pulse': 'pulse 2s infinite cubic-bezier(0.4, 0, 0.6, 1)',
        'spin': 'spin 1s linear infinite',

        'wiggle': 'wiggle 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'flash': 'flash 0.5s cubic-bezier(0.4, 0, 0.6, 1)',
        'shake': 'shake 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'bounce': 'bounce 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) infinite',
      },
      scale: {
        102: '1.02',
        98: '0.98',
      },
      transitionDuration: {
        50: '50ms',
        150: '150ms',
        200: '200ms',
        250: '250ms',
        300: '300ms',
        350: '350ms',
        400: '400ms',
        500: '500ms',
      },
      transitionTimingFunction: {
        'ios': 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        'ios-in': 'cubic-bezier(0.4, 0, 1, 1)',
        'ios-out': 'cubic-bezier(0, 0, 0.2, 1)',
        'ios-spring': 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
      },
      zIndex: {
        60: '60',
        70: '70',
        80: '80',
        90: '90',
      },
    },
  },
  plugins: [
    function ({ addUtilities }) {
      const newUtilities = {
        // Text shadow utilities
        '.text-shadow-sm': {
          textShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
        },
        '.text-shadow': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
        },
        '.text-shadow-md': {
          textShadow: '0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08)',
        },
        '.text-shadow-none': {
          textShadow: 'none',
        },

        // iOS 19 inspired transition utilities
        '.transition-fast': {
          transition: 'all 150ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        },
        '.transition-medium': {
          transition: 'all 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        },
        '.transition-slow': {
          transition: 'all 350ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        },

        // iOS 19 specific transition utilities
        '.transition-ios': {
          transition: 'all 250ms cubic-bezier(0.25, 0.46, 0.45, 0.94)',
        },
        '.transition-ios-in': {
          transition: 'all 200ms cubic-bezier(0.4, 0, 1, 1)',
        },
        '.transition-ios-out': {
          transition: 'all 250ms cubic-bezier(0, 0, 0.2, 1)',
        },
        '.transition-ios-spring': {
          transition: 'all 350ms cubic-bezier(0.175, 0.885, 0.32, 1.275)',
        },

        // iOS 19 backdrop blur utilities
        '.backdrop-blur-xs': {
          backdropFilter: 'blur(2px)',
        },
        '.backdrop-blur-sm': {
          backdropFilter: 'blur(4px)',
        },
        '.backdrop-blur': {
          backdropFilter: 'blur(8px)',
        },
        '.backdrop-blur-md': {
          backdropFilter: 'blur(12px)',
        },
        '.backdrop-blur-lg': {
          backdropFilter: 'blur(16px)',
        },
        '.backdrop-blur-xl': {
          backdropFilter: 'blur(24px)',
        },

        // iOS 19 safe area utilities
        '.pb-safe': {
          paddingBottom: 'env(safe-area-inset-bottom, 0px)',
        },
        '.pt-safe': {
          paddingTop: 'env(safe-area-inset-top, 0px)',
        },
        '.pl-safe': {
          paddingLeft: 'env(safe-area-inset-left, 0px)',
        },
        '.pr-safe': {
          paddingRight: 'env(safe-area-inset-right, 0px)',
        },
        '.px-safe': {
          paddingLeft: 'env(safe-area-inset-left, 0px)',
          paddingRight: 'env(safe-area-inset-right, 0px)',
        },
        '.py-safe': {
          paddingTop: 'env(safe-area-inset-top, 0px)',
          paddingBottom: 'env(safe-area-inset-bottom, 0px)',
        },
        '.p-safe': {
          paddingTop: 'env(safe-area-inset-top, 0px)',
          paddingBottom: 'env(safe-area-inset-bottom, 0px)',
          paddingLeft: 'env(safe-area-inset-left, 0px)',
          paddingRight: 'env(safe-area-inset-right, 0px)',
        },
      }
      addUtilities(newUtilities)
    },
  ],
};
