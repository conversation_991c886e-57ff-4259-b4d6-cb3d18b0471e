{"name": "sunride", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:file": "node scripts/run-test.js", "check:types": "tsc --noEmit", "check:ts": "node scripts/check-typescript.js", "check:any": "node scripts/check-any-types.js", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "verify:deployment": "node scripts/verify-deployment.js", "deploy": "npm run verify:deployment && git push origin main", "vercel:verify": "node scripts/vercel-build-verify.js", "compare:builds": "node scripts/compare-builds.js"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "autoprefixer": "^10.4.21", "chart.js": "^4.4.8", "chartjs-adapter-date-fns": "^3.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.0.0", "framer-motion": "^12.7.2", "gsap": "^3.12.7", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "leaflet-gpx": "^2.1.2", "leaflet.heat": "^0.2.0", "limiter": "^2.1.0", "lucide-react": "^0.484.0", "mongodb": "^6.15.0", "mongoose": "^8.13.0", "next": "15.2.4", "next-themes": "^0.4.6", "ol": "^10.5.0", "pdf-lib": "^1.17.1", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "recharts": "^2.15.2", "server-only": "^0.0.1", "shadcn-ui": "^0.9.5", "swr": "^2.3.3", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.4", "xml2js": "^0.6.2", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.12", "@types/leaflet": "^1.9.17", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/xml2js": "^0.4.14", "eslint": "^9", "eslint-config-next": "15.2.4", "husky": "^9.1.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.5.1", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}