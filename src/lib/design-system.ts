/**
 * Design System Utilities
 * 
 * This file provides utility functions and constants for applying the design system
 * consistently throughout the application.
 */

import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

/**
 * Typography Utilities
 * Consistent text styles for headings, body text, and other typographic elements
 */
export const typography = {
  // Heading styles
  h1: 'text-4xl font-bold tracking-tight leading-tight md:text-5xl',
  h2: 'text-3xl font-bold tracking-tight leading-tight md:text-4xl',
  h3: 'text-2xl font-bold tracking-tight leading-tight md:text-3xl',
  h4: 'text-xl font-bold tracking-tight leading-tight md:text-2xl',
  h5: 'text-lg font-bold tracking-tight leading-tight md:text-xl',
  h6: 'text-base font-bold tracking-tight leading-tight md:text-lg',

  // Body text styles
  body: 'text-base leading-normal',
  bodyLg: 'text-lg leading-relaxed',
  bodySm: 'text-sm leading-normal',
  bodyXs: 'text-xs leading-normal',

  // Special text styles
  lead: 'text-xl leading-relaxed',
  caption: 'text-sm text-muted-foreground',
  overline: 'text-xs uppercase tracking-widest font-medium',
  code: 'font-mono text-sm',
  
  // Text variations
  muted: 'text-muted-foreground',
  subtle: 'text-secondary-foreground',
  strong: 'font-semibold',
  center: 'text-center',
  right: 'text-right',
};

/**
 * Layout Utilities
 * Consistent layout patterns for containers, grids, and other layout elements
 */
export const layout = {
  // Container styles
  container: 'container mx-auto px-4 sm:px-6 lg:px-8',
  containerSm: 'container mx-auto px-4 max-w-3xl',
  containerLg: 'container mx-auto px-4 max-w-7xl',
  
  // Section styles
  section: 'py-12 md:py-16 lg:py-20',
  sectionSm: 'py-8 md:py-12',
  sectionLg: 'py-16 md:py-24 lg:py-32',
  
  // Flex layouts
  flexRow: 'flex flex-row',
  flexCol: 'flex flex-col',
  flexCenter: 'flex items-center justify-center',
  flexBetween: 'flex items-center justify-between',
  flexStart: 'flex items-center justify-start',
  flexEnd: 'flex items-center justify-end',
  
  // Grid layouts
  gridAuto: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6',
  gridSm: 'grid grid-cols-1 sm:grid-cols-2 gap-4',
  gridMd: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6',
  gridLg: 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8',
  
  // Spacing
  gapSm: 'gap-2',
  gapMd: 'gap-4',
  gapLg: 'gap-6',
  gapXl: 'gap-8',
  
  // Padding
  padSm: 'p-2',
  padMd: 'p-4',
  padLg: 'p-6',
  padXl: 'p-8',
};

/**
 * Responsive Utilities
 * Consistent responsive patterns for different screen sizes
 */
export const responsive = {
  // Visibility utilities
  hiddenSm: 'hidden sm:block',
  hiddenMd: 'hidden md:block',
  hiddenLg: 'hidden lg:block',
  hiddenXl: 'hidden xl:block',
  
  visibleSm: 'sm:hidden',
  visibleMd: 'md:hidden',
  visibleLg: 'lg:hidden',
  visibleXl: 'xl:hidden',
  
  // Flex direction utilities
  stackCol: 'flex flex-col',
  stackRow: 'flex flex-row',
  stackColSm: 'flex flex-col sm:flex-row',
  stackColMd: 'flex flex-col md:flex-row',
  stackColLg: 'flex flex-col lg:flex-row',
  
  // Mobile-first width utilities
  fullWidth: 'w-full',
  autoWidth: 'w-auto',
  
  // Container utilities
  containerSm: 'w-full max-w-screen-sm mx-auto px-4',
  containerMd: 'w-full max-w-screen-md mx-auto px-4 sm:px-6',
  containerLg: 'w-full max-w-screen-lg mx-auto px-4 sm:px-6 lg:px-8',
  containerXl: 'w-full max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8',
  container2xl: 'w-full max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8',
  
  // Scrollable container utilities
  scrollContainer: 'overflow-x-auto overflow-y-hidden scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent',
  scrollItem: 'flex-shrink-0 snap-center',
  
  // Chart and map container utilities
  chartContainer: 'h-[350px] sm:h-[400px] md:h-[450px] lg:h-[500px] w-full max-w-full overflow-visible',
  mapContainer: 'h-[300px] sm:h-[400px] md:h-[500px] lg:h-[600px] w-full overflow-hidden',
  
  // Spacing utilities for mobile
  mobileSpacing: 'p-3 sm:p-4 md:p-6',
  mobileGap: 'gap-3 sm:gap-4 md:gap-6',
  mobileMargin: 'mb-4 sm:mb-6 md:mb-8',
};

/**
 * Effects Utilities
 * Consistent visual effects for shadows, borders, and other decorative elements
 */
export const effects = {
  // Shadow effects
  shadowSm: 'shadow-sm',
  shadowMd: 'shadow-md',
  shadowLg: 'shadow-lg',
  shadowXl: 'shadow-xl',
  shadow2xl: 'shadow-2xl',
  shadowNone: 'shadow-none',
  
  // Border effects
  border: 'border border-border',
  borderTop: 'border-t border-border',
  borderRight: 'border-r border-border',
  borderBottom: 'border-b border-border',
  borderLeft: 'border-l border-border',
  borderNone: 'border-0',
  
  // Border radius
  roundedSm: 'rounded-sm',
  roundedMd: 'rounded-md',
  roundedLg: 'rounded-lg',
  roundedXl: 'rounded-xl',
  rounded2xl: 'rounded-2xl',
  roundedFull: 'rounded-full',
  roundedNone: 'rounded-none',
  
  // Card effects
  card: 'bg-card text-card-foreground rounded-lg border border-border shadow-sm',
  cardHover: 'hover:shadow-md transition-shadow duration-200',
  cardActive: 'active:shadow-sm transition-shadow duration-200',
  
  // Glass effects
  glass: 'bg-white/80 dark:bg-zinc-900/80 backdrop-blur-md border border-white/20 dark:border-zinc-800/20',
  glassDark: 'bg-zinc-900/80 backdrop-blur-md border border-zinc-800/20',
  glassLight: 'bg-white/80 backdrop-blur-md border border-white/20',
};

/**
 * Animation Utilities
 * Consistent animation patterns for transitions and micro-interactions
 */
export const animation = {
  // Transition utilities
  transition: 'transition-all duration-200 ease-in-out',
  transitionFast: 'transition-all duration-150 ease-in-out',
  transitionSlow: 'transition-all duration-300 ease-in-out',
  
  // Fade animations
  fadeIn: 'animate-fade-in',
  fadeOut: 'animate-fade-out',
  fadeInSlow: 'animate-fade-in-slow',
  fadeOutSlow: 'animate-fade-out-slow',
  
  // Slide animations
  slideInLeft: 'animate-slide-in-left',
  slideInRight: 'animate-slide-in-right',
  slideInUp: 'animate-slide-in-up',
  slideInDown: 'animate-slide-in-down',
  
  // Scale animations
  scaleIn: 'animate-scale-in',
  scaleOut: 'animate-scale-out',
  
  // Hover animations
  hoverScale: 'hover:scale-105 transition-transform duration-200',
  hoverShadow: 'hover:shadow-md transition-shadow duration-200',
  hoverOpacity: 'hover:opacity-80 transition-opacity duration-200',
  
  // Button animations
  buttonPress: 'active:scale-95 transition-transform duration-200',
};

export { cn };
