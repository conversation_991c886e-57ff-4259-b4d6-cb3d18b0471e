/**
 * Image Utilities
 * 
 * This file provides utilities for optimizing images for different devices and screen sizes.
 */

import { usePerformance } from '@/components/performance/performance-provider';

/**
 * Image quality levels
 */
export type ImageQuality = 'low' | 'medium' | 'high' | 'auto';

/**
 * Image size options
 */
export type ImageSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'original';

/**
 * Image format options
 */
export type ImageFormat = 'webp' | 'avif' | 'jpeg' | 'png' | 'auto';

/**
 * Image optimization options
 */
export interface ImageOptimizationOptions {
  /** Image quality level */
  quality?: ImageQuality;
  /** Image size */
  size?: ImageSize;
  /** Image format */
  format?: ImageFormat;
  /** Whether to use responsive images */
  responsive?: boolean;
  /** Whether to use lazy loading */
  lazy?: boolean;
  /** Whether to use blur-up loading */
  blurUp?: boolean;
  /** Whether to use low quality image placeholders */
  lqip?: boolean;
  /** Whether to optimize for device capabilities */
  optimizeForDevice?: boolean;
  /** Whether to optimize for network conditions */
  optimizeForNetwork?: boolean;
  /** Whether to optimize for battery level */
  optimizeForBattery?: boolean;
  /** Whether to optimize for data saver mode */
  optimizeForDataSaver?: boolean;
  /** Whether to optimize for low memory */
  optimizeForLowMemory?: boolean;
  /** Whether to optimize for low-end devices */
  optimizeForLowEndDevice?: boolean;
}

/**
 * Default image optimization options
 */
export const defaultImageOptimizationOptions: ImageOptimizationOptions = {
  quality: 'auto',
  size: 'original',
  format: 'auto',
  responsive: true,
  lazy: true,
  blurUp: true,
  lqip: true,
  optimizeForDevice: true,
  optimizeForNetwork: true,
  optimizeForBattery: true,
  optimizeForDataSaver: true,
  optimizeForLowMemory: true,
  optimizeForLowEndDevice: true,
};

/**
 * Get image quality based on options and device capabilities
 */
export function getImageQuality(options: ImageOptimizationOptions = {}): number {
  const {
    quality = 'auto',
    optimizeForDevice = true,
    optimizeForNetwork = true,
    optimizeForBattery = true,
    optimizeForDataSaver = true,
    optimizeForLowMemory = true,
    optimizeForLowEndDevice = true,
  } = options;

  // If quality is not auto, use the specified quality
  if (quality !== 'auto') {
    switch (quality) {
      case 'low':
        return 60;
      case 'medium':
        return 75;
      case 'high':
        return 90;
      default:
        return 80;
    }
  }

  // If optimizing for device capabilities, check device performance
  if (optimizeForDevice || optimizeForNetwork || optimizeForBattery || optimizeForDataSaver || optimizeForLowMemory || optimizeForLowEndDevice) {
    // Use the usePerformance hook to get device capabilities
    // Note: This should only be used in a React component
    try {
      const performance = usePerformance();

      // Check if we should reduce image quality
      if (performance.shouldReduceImageQuality) {
        return 60;
      }

      // Check specific conditions
      if (
        (optimizeForDataSaver && performance.isDataSaver) ||
        (optimizeForBattery && performance.isLowBattery) ||
        (optimizeForLowMemory && performance.isLowMemory) ||
        (optimizeForLowEndDevice && performance.isLowEndDevice) ||
        (optimizeForNetwork && (performance.isSlowConnection || performance.connectionType === '2g' || performance.connectionType === 'slow-2g'))
      ) {
        return 60;
      }
    } catch (error) {
      // If usePerformance hook is not available, use default quality
      console.warn('usePerformance hook is not available, using default quality');
    }
  }

  // Default quality
  return 80;
}

/**
 * Get image size dimensions based on options
 */
export function getImageSize(size: ImageSize = 'original'): { width: number; height: number } {
  switch (size) {
    case 'xs':
      return { width: 320, height: 240 };
    case 'sm':
      return { width: 640, height: 480 };
    case 'md':
      return { width: 960, height: 720 };
    case 'lg':
      return { width: 1280, height: 960 };
    case 'xl':
      return { width: 1920, height: 1440 };
    case '2xl':
      return { width: 2560, height: 1920 };
    case 'original':
    default:
      return { width: 0, height: 0 };
  }
}

/**
 * Get image format based on options and browser support
 */
export function getImageFormat(format: ImageFormat = 'auto'): string {
  // If format is not auto, use the specified format
  if (format !== 'auto') {
    return format;
  }

  // Check browser support for modern formats
  if (typeof window !== 'undefined') {
    // Check for WebP support
    const webpSupported = document.createElement('canvas')
      .toDataURL('image/webp')
      .indexOf('data:image/webp') === 0;

    // Check for AVIF support (not widely supported yet)
    const avifSupported = false; // Simplified check, in reality would need more complex detection

    // Use the best supported format
    if (avifSupported) {
      return 'avif';
    } else if (webpSupported) {
      return 'webp';
    }
  }

  // Default to JPEG as a fallback
  return 'jpeg';
}

/**
 * Generate responsive image sources for different screen sizes
 */
export function generateResponsiveSources(
  src: string,
  options: ImageOptimizationOptions = {}
): Array<{ media: string; srcSet: string; type?: string }> {
  const { format = 'auto', quality = 'auto' } = options;
  
  // Get the image format
  const imageFormat = getImageFormat(format);
  
  // Get the image quality
  const imageQuality = getImageQuality(options);
  
  // Generate sources for different screen sizes
  return [
    {
      media: '(max-width: 640px)',
      srcSet: `${src}?w=640&q=${imageQuality}&fmt=${imageFormat}`,
      type: `image/${imageFormat}`,
    },
    {
      media: '(max-width: 768px)',
      srcSet: `${src}?w=768&q=${imageQuality}&fmt=${imageFormat}`,
      type: `image/${imageFormat}`,
    },
    {
      media: '(max-width: 1024px)',
      srcSet: `${src}?w=1024&q=${imageQuality}&fmt=${imageFormat}`,
      type: `image/${imageFormat}`,
    },
    {
      media: '(max-width: 1280px)',
      srcSet: `${src}?w=1280&q=${imageQuality}&fmt=${imageFormat}`,
      type: `image/${imageFormat}`,
    },
    {
      media: '(min-width: 1281px)',
      srcSet: `${src}?w=1920&q=${imageQuality}&fmt=${imageFormat}`,
      type: `image/${imageFormat}`,
    },
  ];
}

/**
 * Generate a low quality image placeholder URL
 */
export function generateLQIP(src: string): string {
  // Generate a low quality image placeholder
  return `${src}?w=20&q=10&blur=10`;
}

/**
 * Hook to get optimized image props based on device capabilities
 */
export function useOptimizedImage(
  src: string,
  alt: string,
  options: ImageOptimizationOptions = {}
) {
  const performance = usePerformance();
  
  // Merge options with defaults
  const mergedOptions: ImageOptimizationOptions = {
    ...defaultImageOptimizationOptions,
    ...options,
  };
  
  // Get image quality based on device capabilities
  const quality = getImageQuality(mergedOptions);
  
  // Get image format based on browser support
  const format = getImageFormat(mergedOptions.format);
  
  // Generate responsive sources
  const sources = mergedOptions.responsive
    ? generateResponsiveSources(src, mergedOptions)
    : [];
  
  // Generate low quality image placeholder
  const placeholderSrc = mergedOptions.lqip ? generateLQIP(src) : undefined;
  
  // Determine if we should use low quality image
  const shouldUseLowQuality = performance.shouldReduceImageQuality;
  
  // Generate low quality image URL
  const lowQualitySrc = shouldUseLowQuality ? `${src}?w=640&q=60&fmt=${format}` : undefined;
  
  // Return optimized image props
  return {
    src,
    alt,
    quality,
    sources,
    placeholderSrc,
    lowQualitySrc,
    blurUp: mergedOptions.blurUp,
    lazy: mergedOptions.lazy,
    optimizeForLowEnd: mergedOptions.optimizeForLowEndDevice,
  };
}

export default {
  getImageQuality,
  getImageSize,
  getImageFormat,
  generateResponsiveSources,
  generateLQIP,
  useOptimizedImage,
};
