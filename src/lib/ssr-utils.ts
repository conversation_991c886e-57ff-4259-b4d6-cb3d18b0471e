/**
 * Server-Side Rendering Utilities
 * 
 * This file provides utilities for optimizing server-side rendering.
 */

import { cache } from 'react';
import { headers } from 'next/headers';

/**
 * Cache options
 */
export interface CacheOptions {
  /** Cache tag */
  tag?: string;
  /** Cache revalidation time in seconds */
  revalidate?: number;
  /** Whether to force cache */
  force?: boolean;
}

/**
 * Preload options
 */
export interface PreloadOptions {
  /** Whether to preload as high priority */
  highPriority?: boolean;
  /** Whether to preload as low priority */
  lowPriority?: boolean;
  /** Whether to preload as prefetch */
  prefetch?: boolean;
  /** Whether to preload as prerender */
  prerender?: boolean;
}

/**
 * Device type
 */
export type DeviceType = 'mobile' | 'tablet' | 'desktop' | 'unknown';

/**
 * Browser type
 */
export type BrowserType = 'chrome' | 'firefox' | 'safari' | 'edge' | 'ie' | 'opera' | 'unknown';

/**
 * OS type
 */
export type OSType = 'windows' | 'macos' | 'linux' | 'ios' | 'android' | 'unknown';

/**
 * Connection type
 */
export type ConnectionType = '4g' | '3g' | '2g' | 'slow-2g' | 'unknown';

/**
 * User agent info
 */
export interface UserAgentInfo {
  /** Device type */
  deviceType: DeviceType;
  /** Browser type */
  browserType: BrowserType;
  /** OS type */
  osType: OSType;
  /** Whether the device is a bot */
  isBot: boolean;
  /** Whether the device is a mobile device */
  isMobile: boolean;
  /** Whether the device is a tablet device */
  isTablet: boolean;
  /** Whether the device is a desktop device */
  isDesktop: boolean;
  /** User agent string */
  userAgent: string;
}

/**
 * Request info
 */
export interface RequestInfo {
  /** User agent info */
  userAgent: UserAgentInfo;
  /** Connection type */
  connectionType: ConnectionType;
  /** Whether the request is from a low-end device */
  isLowEndDevice: boolean;
  /** Whether the request is from a data saver enabled device */
  isDataSaverEnabled: boolean;
  /** Whether the request is from a reduced motion enabled device */
  isReducedMotionEnabled: boolean;
  /** Whether the request is from a high contrast enabled device */
  isHighContrastEnabled: boolean;
  /** Whether the request is from a dark mode enabled device */
  isDarkModeEnabled: boolean;
  /** Whether the request is from a light mode enabled device */
  isLightModeEnabled: boolean;
  /** Whether the request is from a prefers-reduced-data enabled device */
  isPrefersReducedDataEnabled: boolean;
  /** Whether the request is from a prefers-reduced-transparency enabled device */
  isPrefersReducedTransparencyEnabled: boolean;
  /** Whether the request is from a prefers-contrast enabled device */
  isPrefersContrastEnabled: boolean;
  /** Whether the request is from a prefers-color-scheme enabled device */
  isPrefersColorSchemeEnabled: boolean;
  /** Whether the request is from a prefers-reduced-motion enabled device */
  isPrefersReducedMotionEnabled: boolean;
  /** Whether the request is from a prefers-reduced-data enabled device */
  isPrefersReducedData: boolean;
  /** Whether the request is from a prefers-reduced-transparency enabled device */
  isPrefersReducedTransparency: boolean;
  /** Whether the request is from a prefers-contrast enabled device */
  isPrefersContrast: boolean;
  /** Whether the request is from a prefers-color-scheme enabled device */
  isPrefersColorScheme: boolean;
  /** Whether the request is from a prefers-reduced-motion enabled device */
  isPrefersReducedMotion: boolean;
}

/**
 * Get user agent info
 */
export const getUserAgentInfo = cache((userAgent: string): UserAgentInfo => {
  // Default values
  const info: UserAgentInfo = {
    deviceType: 'unknown',
    browserType: 'unknown',
    osType: 'unknown',
    isBot: false,
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    userAgent,
  };
  
  // Check if user agent is a bot
  const botPatterns = [
    'bot', 'crawl', 'spider', 'slurp', 'baiduspider', 'yandexbot',
    'googlebot', 'bingbot', 'facebookexternalhit', 'linkedinbot',
    'twitterbot', 'slackbot', 'telegrambot', 'whatsapp', 'pingdom',
  ];
  
  info.isBot = botPatterns.some(pattern => userAgent.toLowerCase().includes(pattern));
  
  // Detect device type
  if (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent) ||
    /Mobile|mobile|Tablet|tablet/i.test(userAgent)
  ) {
    if (/iPad|Tablet|tablet/i.test(userAgent)) {
      info.deviceType = 'tablet';
      info.isTablet = true;
    } else {
      info.deviceType = 'mobile';
      info.isMobile = true;
    }
  } else {
    info.deviceType = 'desktop';
    info.isDesktop = true;
  }
  
  // Detect browser type
  if (/Chrome|Chromium/i.test(userAgent) && !/Edg|Edge/i.test(userAgent)) {
    info.browserType = 'chrome';
  } else if (/Firefox|FxiOS/i.test(userAgent)) {
    info.browserType = 'firefox';
  } else if (/Safari/i.test(userAgent) && !/Chrome|Chromium/i.test(userAgent)) {
    info.browserType = 'safari';
  } else if (/Edg|Edge/i.test(userAgent)) {
    info.browserType = 'edge';
  } else if (/MSIE|Trident/i.test(userAgent)) {
    info.browserType = 'ie';
  } else if (/Opera|OPR/i.test(userAgent)) {
    info.browserType = 'opera';
  }
  
  // Detect OS type
  if (/Windows/i.test(userAgent)) {
    info.osType = 'windows';
  } else if (/Macintosh|Mac OS X/i.test(userAgent)) {
    info.osType = 'macos';
  } else if (/Linux/i.test(userAgent) && !/Android/i.test(userAgent)) {
    info.osType = 'linux';
  } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
    info.osType = 'ios';
  } else if (/Android/i.test(userAgent)) {
    info.osType = 'android';
  }
  
  return info;
});

/**
 * Get request info
 */
export const getRequestInfo = cache((): RequestInfo => {
  const headersList = headers();
  const userAgent = headersList.get('user-agent') || '';
  const saveData = headersList.get('save-data') === 'on';
  const prefersReducedMotion = headersList.get('sec-ch-prefers-reduced-motion') === 'reduce';
  const prefersColorScheme = headersList.get('sec-ch-prefers-color-scheme') || '';
  const prefersContrast = headersList.get('sec-ch-prefers-contrast') || '';
  
  const userAgentInfo = getUserAgentInfo(userAgent);
  
  return {
    userAgent: userAgentInfo,
    connectionType: 'unknown',
    isLowEndDevice: userAgentInfo.isMobile || userAgentInfo.isTablet,
    isDataSaverEnabled: saveData,
    isReducedMotionEnabled: prefersReducedMotion,
    isHighContrastEnabled: prefersContrast === 'high',
    isDarkModeEnabled: prefersColorScheme === 'dark',
    isLightModeEnabled: prefersColorScheme === 'light',
    isPrefersReducedDataEnabled: saveData,
    isPrefersReducedTransparencyEnabled: false,
    isPrefersContrastEnabled: prefersContrast !== '',
    isPrefersColorSchemeEnabled: prefersColorScheme !== '',
    isPrefersReducedMotionEnabled: prefersReducedMotion,
    isPrefersReducedData: saveData,
    isPrefersReducedTransparency: false,
    isPrefersContrast: prefersContrast !== '',
    isPrefersColorScheme: prefersColorScheme !== '',
    isPrefersReducedMotion: prefersReducedMotion,
  };
});

/**
 * Cached fetch function
 */
export const cachedFetch = cache(
  async (
    url: string,
    options?: RequestInit & { cache?: CacheOptions }
  ) => {
    const cacheOptions = options?.cache;
    
    const fetchOptions: RequestInit = { ...options };
    delete (fetchOptions as any).cache;
    
    const response = await fetch(url, {
      ...fetchOptions,
      next: {
        tags: cacheOptions?.tag ? [cacheOptions.tag] : undefined,
        revalidate: cacheOptions?.revalidate,
        ...(cacheOptions?.force ? { cache: 'force-cache' } : {}),
      },
    });
    
    return response;
  }
);

/**
 * Preload resource
 */
export const preloadResource = (
  url: string,
  as: 'image' | 'style' | 'script' | 'font' | 'fetch',
  options?: PreloadOptions
) => {
  if (typeof document === 'undefined') return;
  
  const link = document.createElement('link');
  link.rel = options?.prefetch ? 'prefetch' : 'preload';
  link.href = url;
  link.as = as;
  
  if (options?.highPriority) {
    link.importance = 'high';
  } else if (options?.lowPriority) {
    link.importance = 'low';
  }
  
  if (options?.prerender) {
    link.rel = 'prerender';
  }
  
  document.head.appendChild(link);
};

/**
 * Preload image
 */
export const preloadImage = (url: string, options?: PreloadOptions) => {
  preloadResource(url, 'image', options);
};

/**
 * Preload style
 */
export const preloadStyle = (url: string, options?: PreloadOptions) => {
  preloadResource(url, 'style', options);
};

/**
 * Preload script
 */
export const preloadScript = (url: string, options?: PreloadOptions) => {
  preloadResource(url, 'script', options);
};

/**
 * Preload font
 */
export const preloadFont = (url: string, options?: PreloadOptions) => {
  preloadResource(url, 'font', options);
};

/**
 * Preload fetch
 */
export const preloadFetch = (url: string, options?: PreloadOptions) => {
  preloadResource(url, 'fetch', options);
};

/**
 * Optimize images for device
 */
export const optimizeImagesForDevice = (
  images: Array<{ src: string; width: number; height: number }>,
  deviceType: DeviceType
): Array<{ src: string; width: number; height: number }> => {
  return images.map(image => {
    let optimizedImage = { ...image };
    
    // Optimize image based on device type
    if (deviceType === 'mobile') {
      // Reduce image size for mobile devices
      optimizedImage.width = Math.min(image.width, 640);
      optimizedImage.height = Math.round((optimizedImage.width / image.width) * image.height);
      optimizedImage.src = `${image.src}?w=${optimizedImage.width}&q=75`;
    } else if (deviceType === 'tablet') {
      // Reduce image size for tablet devices
      optimizedImage.width = Math.min(image.width, 1024);
      optimizedImage.height = Math.round((optimizedImage.width / image.width) * image.height);
      optimizedImage.src = `${image.src}?w=${optimizedImage.width}&q=80`;
    } else {
      // Use original image size for desktop devices
      optimizedImage.src = `${image.src}?w=${image.width}&q=85`;
    }
    
    return optimizedImage;
  });
};

export default {
  getUserAgentInfo,
  getRequestInfo,
  cachedFetch,
  preloadResource,
  preloadImage,
  preloadStyle,
  preloadScript,
  preloadFont,
  preloadFetch,
  optimizeImagesForDevice,
};
