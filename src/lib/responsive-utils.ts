/**
 * Responsive Utilities
 * 
 * This file provides utilities for responsive design.
 */

import { useEffect, useState } from 'react';

/**
 * Breakpoint sizes
 */
export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Breakpoint values in pixels
 */
export const breakpoints: Record<Breakpoint, number> = {
  xs: 0,
  sm: 375,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

/**
 * Spacing sizes
 */
export type SpacingSize = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';

/**
 * Spacing values in pixels
 */
export const spacing: Record<SpacingSize, number> = {
  none: 0,
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
};

/**
 * Container max widths
 */
export type ContainerSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';

/**
 * Container max width values in pixels
 */
export const containerMaxWidths: Record<ContainerSize, string> = {
  xs: '320px',
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
  full: '100%',
  none: 'none',
};

/**
 * Grid column counts
 */
export type GridColumns = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12;

/**
 * Grid column span
 */
export type GridColumnSpan = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 'full';

/**
 * Responsive value for different breakpoints
 */
export type ResponsiveValue<T> = {
  xs?: T;
  sm?: T;
  md?: T;
  lg?: T;
  xl?: T;
  '2xl'?: T;
};

/**
 * Get spacing value in pixels
 */
export function getSpacing(size: SpacingSize): number {
  return spacing[size];
}

/**
 * Get spacing value in rem
 */
export function getSpacingRem(size: SpacingSize): string {
  return `${spacing[size] / 16}rem`;
}

/**
 * Get container max width
 */
export function getContainerMaxWidth(size: ContainerSize): string {
  return containerMaxWidths[size];
}

/**
 * Get responsive value for current breakpoint
 */
export function getResponsiveValue<T>(
  values: ResponsiveValue<T>,
  currentBreakpoint: Breakpoint,
  defaultValue: T
): T {
  // Get breakpoints in descending order
  const breakpointOrder: Breakpoint[] = ['2xl', 'xl', 'lg', 'md', 'sm', 'xs'];
  
  // Find the first breakpoint that is less than or equal to the current breakpoint
  const index = breakpointOrder.indexOf(currentBreakpoint);
  
  // Check each breakpoint in descending order
  for (let i = index; i < breakpointOrder.length; i++) {
    const breakpoint = breakpointOrder[i];
    if (values[breakpoint] !== undefined) {
      return values[breakpoint] as T;
    }
  }
  
  // Return default value if no matching breakpoint is found
  return defaultValue;
}

/**
 * Hook to get current breakpoint
 */
export function useBreakpoint(): Breakpoint {
  const [breakpoint, setBreakpoint] = useState<Breakpoint>('xs');
  
  useEffect(() => {
    // Function to update breakpoint
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      
      if (width >= breakpoints['2xl']) {
        setBreakpoint('2xl');
      } else if (width >= breakpoints.xl) {
        setBreakpoint('xl');
      } else if (width >= breakpoints.lg) {
        setBreakpoint('lg');
      } else if (width >= breakpoints.md) {
        setBreakpoint('md');
      } else if (width >= breakpoints.sm) {
        setBreakpoint('sm');
      } else {
        setBreakpoint('xs');
      }
    };
    
    // Initial update
    updateBreakpoint();
    
    // Add resize event listener
    window.addEventListener('resize', updateBreakpoint);
    
    // Clean up
    return () => {
      window.removeEventListener('resize', updateBreakpoint);
    };
  }, []);
  
  return breakpoint;
}

/**
 * Hook to check if screen is mobile
 */
export function useMobile(): boolean {
  const breakpoint = useBreakpoint();
  return breakpoint === 'xs' || breakpoint === 'sm';
}

/**
 * Hook to check if screen is tablet
 */
export function useTablet(): boolean {
  const breakpoint = useBreakpoint();
  return breakpoint === 'md';
}

/**
 * Hook to check if screen is desktop
 */
export function useDesktop(): boolean {
  const breakpoint = useBreakpoint();
  return breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl';
}

/**
 * Hook to check if screen is in portrait orientation
 */
export function usePortrait(): boolean {
  const [isPortrait, setIsPortrait] = useState(false);
  
  useEffect(() => {
    // Function to update orientation
    const updateOrientation = () => {
      setIsPortrait(window.innerHeight > window.innerWidth);
    };
    
    // Initial update
    updateOrientation();
    
    // Add resize event listener
    window.addEventListener('resize', updateOrientation);
    
    // Clean up
    return () => {
      window.removeEventListener('resize', updateOrientation);
    };
  }, []);
  
  return isPortrait;
}

/**
 * Hook to get responsive value for current breakpoint
 */
export function useResponsiveValue<T>(
  values: ResponsiveValue<T>,
  defaultValue: T
): T {
  const breakpoint = useBreakpoint();
  return getResponsiveValue(values, breakpoint, defaultValue);
}

/**
 * Hook to get container max width for current breakpoint
 */
export function useContainerMaxWidth(
  size: ContainerSize | ResponsiveValue<ContainerSize> = 'xl'
): string {
  const breakpoint = useBreakpoint();
  
  // If size is a responsive value, get the value for the current breakpoint
  if (typeof size === 'object') {
    const containerSize = getResponsiveValue(size, breakpoint, 'xl');
    return getContainerMaxWidth(containerSize);
  }
  
  // If size is a string, return the container max width
  return getContainerMaxWidth(size);
}

/**
 * Hook to get spacing for current breakpoint
 */
export function useSpacing(
  size: SpacingSize | ResponsiveValue<SpacingSize> = 'md'
): string {
  const breakpoint = useBreakpoint();
  
  // If size is a responsive value, get the value for the current breakpoint
  if (typeof size === 'object') {
    const spacingSize = getResponsiveValue(size, breakpoint, 'md');
    return getSpacingRem(spacingSize);
  }
  
  // If size is a string, return the spacing
  return getSpacingRem(size);
}

/**
 * Hook to get grid columns for current breakpoint
 */
export function useGridColumns(
  columns: GridColumns | ResponsiveValue<GridColumns> = 12
): number {
  const breakpoint = useBreakpoint();
  
  // If columns is a responsive value, get the value for the current breakpoint
  if (typeof columns === 'object') {
    return getResponsiveValue(columns, breakpoint, 12);
  }
  
  // If columns is a number, return the columns
  return columns;
}

/**
 * Hook to get grid column span for current breakpoint
 */
export function useGridColumnSpan(
  span: GridColumnSpan | ResponsiveValue<GridColumnSpan> = 1
): string {
  const breakpoint = useBreakpoint();
  
  // If span is a responsive value, get the value for the current breakpoint
  if (typeof span === 'object') {
    const columnSpan = getResponsiveValue(span, breakpoint, 1);
    return columnSpan === 'full' ? '1 / -1' : `span ${columnSpan} / span ${columnSpan}`;
  }
  
  // If span is a string or number, return the span
  return span === 'full' ? '1 / -1' : `span ${span} / span ${span}`;
}

export default {
  breakpoints,
  spacing,
  containerMaxWidths,
  getSpacing,
  getSpacingRem,
  getContainerMaxWidth,
  getResponsiveValue,
  useBreakpoint,
  useMobile,
  useTablet,
  useDesktop,
  usePortrait,
  useResponsiveValue,
  useContainerMaxWidth,
  useSpacing,
  useGridColumns,
  useGridColumnSpan,
};
