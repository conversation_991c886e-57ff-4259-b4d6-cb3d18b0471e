/**
 * Analytics Utilities
 * 
 * This file provides utilities for tracking analytics data.
 */

import { useEffect, useState } from 'react';
import { createPerformanceMetric } from './performance-utils';

/**
 * Analytics event types
 */
export type AnalyticsEventType =
  | 'page_view'
  | 'component_render'
  | 'user_interaction'
  | 'error'
  | 'performance'
  | 'navigation'
  | 'resource'
  | 'custom';

/**
 * Analytics event
 */
export interface AnalyticsEvent {
  /** Event type */
  type: AnalyticsEventType;
  /** Event name */
  name: string;
  /** Event data */
  data?: Record<string, any>;
  /** Event timestamp */
  timestamp: number;
  /** Event duration (if applicable) */
  duration?: number;
  /** Event source */
  source?: string;
  /** Event category */
  category?: string;
  /** Event label */
  label?: string;
  /** Event value */
  value?: number;
}

/**
 * Analytics provider
 */
export interface AnalyticsProvider {
  /** Provider name */
  name: string;
  /** Track event */
  trackEvent: (event: AnalyticsEvent) => void;
  /** Track page view */
  trackPageView: (path: string, title: string, referrer?: string) => void;
  /** Track error */
  trackError: (error: Error, context?: Record<string, any>) => void;
  /** Track performance */
  trackPerformance: (metric: string, value: number, unit?: string) => void;
  /** Initialize provider */
  initialize: () => void;
}

/**
 * Analytics configuration
 */
export interface AnalyticsConfig {
  /** Whether to enable analytics */
  enabled: boolean;
  /** Whether to track page views */
  trackPageViews: boolean;
  /** Whether to track errors */
  trackErrors: boolean;
  /** Whether to track performance */
  trackPerformance: boolean;
  /** Whether to track user interactions */
  trackUserInteractions: boolean;
  /** Whether to track resource loading */
  trackResources: boolean;
  /** Whether to track navigation */
  trackNavigation: boolean;
  /** Whether to track component renders */
  trackComponentRenders: boolean;
  /** Whether to track custom events */
  trackCustomEvents: boolean;
  /** Whether to use debug mode */
  debug: boolean;
  /** Sampling rate (0-1) */
  samplingRate: number;
  /** User ID */
  userId?: string;
  /** Session ID */
  sessionId?: string;
  /** Providers */
  providers: AnalyticsProvider[];
}

/**
 * Default analytics configuration
 */
export const defaultAnalyticsConfig: AnalyticsConfig = {
  enabled: process.env.NODE_ENV === 'production',
  trackPageViews: true,
  trackErrors: true,
  trackPerformance: true,
  trackUserInteractions: true,
  trackResources: true,
  trackNavigation: true,
  trackComponentRenders: false,
  trackCustomEvents: true,
  debug: process.env.NODE_ENV === 'development',
  samplingRate: 1,
  providers: [],
};

/**
 * Analytics instance
 */
class Analytics {
  private config: AnalyticsConfig;
  private initialized: boolean = false;
  private events: AnalyticsEvent[] = [];
  private queue: AnalyticsEvent[] = [];
  private flushInterval: NodeJS.Timeout | null = null;

  constructor(config: Partial<AnalyticsConfig> = {}) {
    this.config = { ...defaultAnalyticsConfig, ...config };
  }

  /**
   * Initialize analytics
   */
  public initialize(): void {
    if (this.initialized) return;

    // Initialize providers
    this.config.providers.forEach(provider => {
      try {
        provider.initialize();
      } catch (error) {
        console.error(`Error initializing analytics provider ${provider.name}:`, error);
      }
    });

    // Set up flush interval
    this.flushInterval = setInterval(() => {
      this.flush();
    }, 5000);

    // Set up performance observers
    this.setupPerformanceObservers();

    // Set up error tracking
    if (this.config.trackErrors) {
      this.setupErrorTracking();
    }

    // Set up navigation tracking
    if (this.config.trackNavigation) {
      this.setupNavigationTracking();
    }

    this.initialized = true;
    this.debug('Analytics initialized');
  }

  /**
   * Track event
   */
  public trackEvent(event: Omit<AnalyticsEvent, 'timestamp'>): void {
    if (!this.config.enabled) return;
    if (!this.shouldSample()) return;

    const fullEvent: AnalyticsEvent = {
      ...event,
      timestamp: Date.now(),
    };

    this.queue.push(fullEvent);
    this.debug('Event queued', fullEvent);

    // Flush immediately for certain event types
    if (event.type === 'error' || event.type === 'page_view') {
      this.flush();
    }
  }

  /**
   * Track page view
   */
  public trackPageView(path: string, title: string, referrer?: string): void {
    if (!this.config.trackPageViews) return;

    this.trackEvent({
      type: 'page_view',
      name: 'page_view',
      data: {
        path,
        title,
        referrer,
        url: typeof window !== 'undefined' ? window.location.href : '',
      },
    });
  }

  /**
   * Track error
   */
  public trackError(error: Error, context?: Record<string, any>): void {
    if (!this.config.trackErrors) return;

    this.trackEvent({
      type: 'error',
      name: 'error',
      data: {
        message: error.message,
        stack: error.stack,
        ...context,
      },
    });
  }

  /**
   * Track performance
   */
  public trackPerformance(metric: string, value: number, unit?: string): void {
    if (!this.config.trackPerformance) return;

    this.trackEvent({
      type: 'performance',
      name: metric,
      data: {
        value,
        unit,
      },
    });
  }

  /**
   * Track component render
   */
  public trackComponentRender(
    componentName: string,
    renderTime: number,
    props?: Record<string, any>
  ): void {
    if (!this.config.trackComponentRenders) return;

    this.trackEvent({
      type: 'component_render',
      name: 'component_render',
      data: {
        componentName,
        renderTime,
        props,
      },
      duration: renderTime,
    });
  }

  /**
   * Track user interaction
   */
  public trackUserInteraction(
    action: string,
    element: string,
    value?: string | number,
    context?: Record<string, any>
  ): void {
    if (!this.config.trackUserInteractions) return;

    this.trackEvent({
      type: 'user_interaction',
      name: action,
      data: {
        element,
        value,
        ...context,
      },
    });
  }

  /**
   * Track custom event
   */
  public trackCustomEvent(
    name: string,
    data?: Record<string, any>,
    category?: string,
    label?: string,
    value?: number
  ): void {
    if (!this.config.trackCustomEvents) return;

    this.trackEvent({
      type: 'custom',
      name,
      data,
      category,
      label,
      value,
    });
  }

  /**
   * Flush events
   */
  private flush(): void {
    if (this.queue.length === 0) return;

    // Copy events to local array and clear queue
    const events = [...this.queue];
    this.queue = [];

    // Store events in history
    this.events.push(...events);

    // Limit history size
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }

    // Send events to providers
    this.config.providers.forEach(provider => {
      events.forEach(event => {
        try {
          provider.trackEvent(event);
        } catch (error) {
          console.error(`Error tracking event with provider ${provider.name}:`, error);
        }
      });
    });

    this.debug(`Flushed ${events.length} events`);
  }

  /**
   * Set up performance observers
   */
  private setupPerformanceObservers(): void {
    if (!this.config.trackPerformance) return;
    if (typeof window === 'undefined') return;

    // Web Vitals
    try {
      const observer = new PerformanceObserver(entryList => {
        entryList.getEntries().forEach(entry => {
          // Process different entry types
          switch (entry.entryType) {
            case 'largest-contentful-paint':
              this.trackPerformance('LCP', entry.startTime, 'ms');
              break;
            case 'first-input':
              const fid = (entry as any).processingStart - (entry as any).startTime;
              this.trackPerformance('FID', fid, 'ms');
              break;
            case 'layout-shift':
              if (!(entry as any).hadRecentInput) {
                this.trackPerformance('CLS', (entry as any).value, 'score');
              }
              break;
          }
        });
      });

      // Observe different entry types
      observer.observe({ type: 'largest-contentful-paint', buffered: true });
      observer.observe({ type: 'first-input', buffered: true });
      observer.observe({ type: 'layout-shift', buffered: true });
    } catch (error) {
      console.error('Error setting up performance observers:', error);
    }

    // Resource timing
    if (this.config.trackResources) {
      try {
        const resourceObserver = new PerformanceObserver(entryList => {
          entryList.getEntries().forEach(entry => {
            if (entry.entryType === 'resource') {
              const resource = entry as PerformanceResourceTiming;
              this.trackEvent({
                type: 'resource',
                name: 'resource_timing',
                data: {
                  name: resource.name,
                  initiatorType: resource.initiatorType,
                  duration: resource.duration,
                  transferSize: resource.transferSize,
                  decodedBodySize: resource.decodedBodySize,
                },
                duration: resource.duration,
              });
            }
          });
        });

        resourceObserver.observe({ type: 'resource', buffered: true });
      } catch (error) {
        console.error('Error setting up resource observer:', error);
      }
    }
  }

  /**
   * Set up error tracking
   */
  private setupErrorTracking(): void {
    if (typeof window === 'undefined') return;

    // Global error handler
    window.addEventListener('error', event => {
      this.trackError(event.error || new Error(event.message), {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      });
    });

    // Unhandled promise rejection handler
    window.addEventListener('unhandledrejection', event => {
      const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
      this.trackError(error, {
        type: 'unhandledrejection',
      });
    });
  }

  /**
   * Set up navigation tracking
   */
  private setupNavigationTracking(): void {
    if (typeof window === 'undefined') return;

    // Track initial page view
    this.trackPageView(
      window.location.pathname,
      document.title,
      document.referrer
    );

    // Track navigation changes
    if ('navigation' in window.performance) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        this.trackEvent({
          type: 'navigation',
          name: 'navigation_timing',
          data: {
            type: navigation.type,
            redirectCount: navigation.redirectCount,
            domComplete: navigation.domComplete,
            domInteractive: navigation.domInteractive,
            loadEventEnd: navigation.loadEventEnd,
            loadEventStart: navigation.loadEventStart,
            domContentLoadedEventEnd: navigation.domContentLoadedEventEnd,
            domContentLoadedEventStart: navigation.domContentLoadedEventStart,
            connectEnd: navigation.connectEnd,
            connectStart: navigation.connectStart,
            domainLookupEnd: navigation.domainLookupEnd,
            domainLookupStart: navigation.domainLookupStart,
            fetchStart: navigation.fetchStart,
            requestStart: navigation.requestStart,
            responseEnd: navigation.responseEnd,
            responseStart: navigation.responseStart,
            secureConnectionStart: navigation.secureConnectionStart,
            startTime: navigation.startTime,
            transferSize: navigation.transferSize,
            encodedBodySize: navigation.encodedBodySize,
            decodedBodySize: navigation.decodedBodySize,
          },
        });
      }
    }
  }

  /**
   * Debug log
   */
  private debug(message: string, data?: any): void {
    if (!this.config.debug) return;
    if (data) {
      console.log(`[Analytics] ${message}:`, data);
    } else {
      console.log(`[Analytics] ${message}`);
    }
  }

  /**
   * Determine if event should be sampled
   */
  private shouldSample(): boolean {
    return Math.random() < this.config.samplingRate;
  }

  /**
   * Get events
   */
  public getEvents(): AnalyticsEvent[] {
    return [...this.events];
  }

  /**
   * Get configuration
   */
  public getConfig(): AnalyticsConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<AnalyticsConfig>): void {
    this.config = { ...this.config, ...config };
    this.debug('Configuration updated', this.config);
  }

  /**
   * Clean up
   */
  public cleanup(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }

    this.flush();
    this.initialized = false;
    this.debug('Analytics cleaned up');
  }
}

// Create singleton instance
export const analytics = new Analytics();

/**
 * Hook to use analytics
 */
export function useAnalytics() {
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    if (!isInitialized) {
      analytics.initialize();
      setIsInitialized(true);
    }

    // Track page view
    if (typeof window !== 'undefined') {
      analytics.trackPageView(
        window.location.pathname,
        document.title,
        document.referrer
      );
    }

    return () => {
      // No cleanup needed for singleton
    };
  }, [isInitialized]);

  return analytics;
}

export default analytics;
