/**
 * Accessibility Utilities
 * 
 * This file provides utilities for improving accessibility.
 */

import { useEffect, useState, useCallback, useRef } from 'react';

/**
 * ARIA roles
 */
export type AriaRole =
  | 'alert'
  | 'alertdialog'
  | 'application'
  | 'article'
  | 'banner'
  | 'button'
  | 'cell'
  | 'checkbox'
  | 'columnheader'
  | 'combobox'
  | 'complementary'
  | 'contentinfo'
  | 'definition'
  | 'dialog'
  | 'directory'
  | 'document'
  | 'feed'
  | 'figure'
  | 'form'
  | 'grid'
  | 'gridcell'
  | 'group'
  | 'heading'
  | 'img'
  | 'link'
  | 'list'
  | 'listbox'
  | 'listitem'
  | 'log'
  | 'main'
  | 'marquee'
  | 'math'
  | 'menu'
  | 'menubar'
  | 'menuitem'
  | 'menuitemcheckbox'
  | 'menuitemradio'
  | 'navigation'
  | 'none'
  | 'note'
  | 'option'
  | 'presentation'
  | 'progressbar'
  | 'radio'
  | 'radiogroup'
  | 'region'
  | 'row'
  | 'rowgroup'
  | 'rowheader'
  | 'scrollbar'
  | 'search'
  | 'searchbox'
  | 'separator'
  | 'slider'
  | 'spinbutton'
  | 'status'
  | 'switch'
  | 'tab'
  | 'table'
  | 'tablist'
  | 'tabpanel'
  | 'term'
  | 'textbox'
  | 'timer'
  | 'toolbar'
  | 'tooltip'
  | 'tree'
  | 'treegrid'
  | 'treeitem';

/**
 * ARIA live region politeness
 */
export type AriaLive = 'off' | 'polite' | 'assertive';

/**
 * ARIA relevant values
 */
export type AriaRelevant = 'additions' | 'removals' | 'text' | 'all';

/**
 * ARIA sort values
 */
export type AriaSort = 'none' | 'ascending' | 'descending' | 'other';

/**
 * ARIA orientation values
 */
export type AriaOrientation = 'horizontal' | 'vertical';

/**
 * ARIA current values
 */
export type AriaCurrent =
  | 'page'
  | 'step'
  | 'location'
  | 'date'
  | 'time'
  | 'true'
  | 'false';

/**
 * ARIA has popup values
 */
export type AriaHasPopup =
  | 'false'
  | 'true'
  | 'menu'
  | 'listbox'
  | 'tree'
  | 'grid'
  | 'dialog';

/**
 * ARIA invalid values
 */
export type AriaInvalid = 'false' | 'true' | 'grammar' | 'spelling';

/**
 * ARIA pressed values
 */
export type AriaPressed = 'false' | 'true' | 'mixed' | 'undefined';

/**
 * ARIA expanded values
 */
export type AriaExpanded = 'false' | 'true' | 'undefined';

/**
 * ARIA checked values
 */
export type AriaChecked = 'false' | 'true' | 'mixed' | 'undefined';

/**
 * ARIA selected values
 */
export type AriaSelected = 'false' | 'true' | 'undefined';

/**
 * ARIA busy values
 */
export type AriaBusy = 'false' | 'true';

/**
 * ARIA hidden values
 */
export type AriaHidden = 'false' | 'true' | 'undefined';

/**
 * ARIA disabled values
 */
export type AriaDisabled = 'false' | 'true';

/**
 * ARIA readonly values
 */
export type AriaReadonly = 'false' | 'true';

/**
 * ARIA required values
 */
export type AriaRequired = 'false' | 'true';

/**
 * ARIA grabbed values
 */
export type AriaGrabbed = 'false' | 'true' | 'undefined';

/**
 * ARIA dropeffect values
 */
export type AriaDropeffect = 'none' | 'copy' | 'execute' | 'link' | 'move' | 'popup';

/**
 * ARIA attributes
 */
export interface AriaAttributes {
  /** Identifies the element (or elements) whose contents or presence are controlled by the current element. */
  'aria-controls'?: string;
  /** Indicates the element that represents the current item within a container or set of related elements. */
  'aria-current'?: AriaCurrent;
  /** Indicates an element is being modified and that assistive technologies MAY want to wait until the modifications are complete before exposing them to the user. */
  'aria-busy'?: AriaBusy;
  /** Indicates that the element is perceivable but disabled, so it is not editable or otherwise operable. */
  'aria-disabled'?: AriaDisabled;
  /** Indicates whether the element, or another grouping element it controls, is currently expanded or collapsed. */
  'aria-expanded'?: AriaExpanded;
  /** Indicates the availability and type of interactive popup element, such as menu or dialog, that can be triggered by an element. */
  'aria-haspopup'?: AriaHasPopup;
  /** Indicates whether the element is exposed to an accessibility API. */
  'aria-hidden'?: AriaHidden;
  /** Indicates the entered value does not conform to the format expected by the application. */
  'aria-invalid'?: AriaInvalid;
  /** Indicates that an element will be updated, and describes the types of updates the user agents, assistive technologies, and user can expect from the live region. */
  'aria-live'?: AriaLive;
  /** Indicates whether an element is modal when displayed. */
  'aria-modal'?: boolean;
  /** Indicates whether a text box accepts multiple lines of input or only a single line. */
  'aria-multiline'?: boolean;
  /** Indicates that the user may select more than one item from the current selectable descendants. */
  'aria-multiselectable'?: boolean;
  /** Indicates whether the element's orientation is horizontal, vertical, or unknown/ambiguous. */
  'aria-orientation'?: AriaOrientation;
  /** Indicates that the element is not editable, but is otherwise operable. */
  'aria-readonly'?: AriaReadonly;
  /** Indicates that user input is required on the element before a form may be submitted. */
  'aria-required'?: AriaRequired;
  /** Indicates the current "selected" state of various widgets. */
  'aria-selected'?: AriaSelected;
  /** Indicates if items in a table or grid are sorted in ascending or descending order. */
  'aria-sort'?: AriaSort;
  /** Defines a human-readable, author-localized description for the role of an element. */
  'aria-roledescription'?: string;
  /** Defines a string value that labels the current element. */
  'aria-label'?: string;
  /** Identifies the element (or elements) that labels the current element. */
  'aria-labelledby'?: string;
  /** Identifies the element (or elements) that describes the object. */
  'aria-describedby'?: string;
  /** Identifies the element (or elements) that provide a detailed, extended description for the object. */
  'aria-details'?: string;
  /** Identifies the element that provides an error message for the object. */
  'aria-errormessage'?: string;
  /** Indicates the entered value does not conform to the format expected by the application. */
  'aria-invalid'?: AriaInvalid;
  /** Indicates the element that represents the current item within a container or set of related elements. */
  'aria-current'?: AriaCurrent;
  /** Indicates an element is being modified and that assistive technologies MAY want to wait until the modifications are complete before exposing them to the user. */
  'aria-busy'?: AriaBusy;
  /** Indicates that the element is perceivable but disabled, so it is not editable or otherwise operable. */
  'aria-disabled'?: AriaDisabled;
  /** Indicates whether the element, or another grouping element it controls, is currently expanded or collapsed. */
  'aria-expanded'?: AriaExpanded;
  /** Indicates the availability and type of interactive popup element, such as menu or dialog, that can be triggered by an element. */
  'aria-haspopup'?: AriaHasPopup;
  /** Indicates whether the element is exposed to an accessibility API. */
  'aria-hidden'?: AriaHidden;
  /** Indicates the entered value does not conform to the format expected by the application. */
  'aria-invalid'?: AriaInvalid;
  /** Indicates that an element will be updated, and describes the types of updates the user agents, assistive technologies, and user can expect from the live region. */
  'aria-live'?: AriaLive;
  /** Indicates whether an element is modal when displayed. */
  'aria-modal'?: boolean;
  /** Indicates whether a text box accepts multiple lines of input or only a single line. */
  'aria-multiline'?: boolean;
  /** Indicates that the user may select more than one item from the current selectable descendants. */
  'aria-multiselectable'?: boolean;
  /** Indicates whether the element's orientation is horizontal, vertical, or unknown/ambiguous. */
  'aria-orientation'?: AriaOrientation;
  /** Indicates that the element is not editable, but is otherwise operable. */
  'aria-readonly'?: AriaReadonly;
  /** Indicates that user input is required on the element before a form may be submitted. */
  'aria-required'?: AriaRequired;
  /** Indicates the current "selected" state of various widgets. */
  'aria-selected'?: AriaSelected;
  /** Indicates if items in a table or grid are sorted in ascending or descending order. */
  'aria-sort'?: AriaSort;
  /** Defines a human-readable, author-localized description for the role of an element. */
  'aria-roledescription'?: string;
}

/**
 * Hook to announce messages to screen readers
 */
export function useAnnounce() {
  const [message, setMessage] = useState('');
  const [politeness, setPoliteness] = useState<AriaLive>('polite');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  const announce = useCallback((text: string, level: AriaLive = 'polite') => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    // Set politeness level
    setPoliteness(level);
    
    // Set message
    setMessage(text);
    
    // Clear message after a delay to allow for re-announcing the same message
    timeoutRef.current = setTimeout(() => {
      setMessage('');
    }, 1000);
  }, []);
  
  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);
  
  return {
    announce,
    message,
    politeness,
  };
}

/**
 * Hook to trap focus within a container
 */
export function useFocusTrap(active: boolean = true) {
  const containerRef = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!active) return;
    
    const container = containerRef.current;
    if (!container) return;
    
    // Find all focusable elements
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;
    
    // Focus the first element when the trap is activated
    firstElement?.focus();
    
    // Handle tab key to keep focus within the container
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;
      
      if (e.shiftKey) {
        // If shift+tab and on first element, go to last element
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        // If tab and on last element, go to first element
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };
    
    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [active]);
  
  return containerRef;
}

/**
 * Hook to detect keyboard navigation
 */
export function useKeyboardNavigation() {
  const [isKeyboardUser, setIsKeyboardUser] = useState(false);
  
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Tab') {
        setIsKeyboardUser(true);
      }
    };
    
    const handleMouseDown = () => {
      setIsKeyboardUser(false);
    };
    
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleMouseDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleMouseDown);
    };
  }, []);
  
  return isKeyboardUser;
}

/**
 * Hook to detect screen reader
 */
export function useScreenReader() {
  const [isScreenReaderUser, setIsScreenReaderUser] = useState(false);
  
  useEffect(() => {
    // Check for common screen reader user agent strings
    const userAgent = navigator.userAgent.toLowerCase();
    const isScreenReader = 
      userAgent.includes('jaws') ||
      userAgent.includes('nvda') ||
      userAgent.includes('voiceover') ||
      userAgent.includes('talkback');
    
    setIsScreenReaderUser(isScreenReader);
  }, []);
  
  return isScreenReaderUser;
}

/**
 * Hook to detect high contrast mode
 */
export function useHighContrast() {
  const [isHighContrast, setIsHighContrast] = useState(false);
  
  useEffect(() => {
    // Check for high contrast mode
    const mediaQuery = window.matchMedia('(forced-colors: active)');
    setIsHighContrast(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setIsHighContrast(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);
  
  return isHighContrast;
}

/**
 * Hook to detect reduced motion preference
 */
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);
  
  useEffect(() => {
    // Check for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);
    
    const handleChange = (e: MediaQueryListEvent) => {
      setPrefersReducedMotion(e.matches);
    };
    
    mediaQuery.addEventListener('change', handleChange);
    
    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, []);
  
  return prefersReducedMotion;
}

export default {
  useAnnounce,
  useFocusTrap,
  useKeyboardNavigation,
  useScreenReader,
  useHighContrast,
  useReducedMotion,
};
