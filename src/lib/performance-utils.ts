/**
 * Performance Utilities
 * 
 * This file provides utilities for monitoring and optimizing performance.
 */

/**
 * Performance metric types
 */
export type PerformanceMetricType = 
  | 'FCP' // First Contentful Paint
  | 'LCP' // Largest Contentful Paint
  | 'FID' // First Input Delay
  | 'CLS' // Cumulative Layout Shift
  | 'TTI' // Time to Interactive
  | 'TBT' // Total Blocking Time
  | 'TTFB' // Time to First Byte
  | 'INP' // Interaction to Next Paint
  | 'Custom'; // Custom metric

/**
 * Performance metric
 */
export interface PerformanceMetric {
  /** Metric name */
  name: string;
  /** Metric type */
  type: PerformanceMetricType;
  /** Metric value */
  value: number;
  /** Metric unit */
  unit: 'ms' | 's' | 'score' | 'count' | 'bytes' | 'percent' | 'custom';
  /** Metric timestamp */
  timestamp: number;
  /** Whether the metric is good */
  isGood: boolean;
  /** Whether the metric needs improvement */
  needsImprovement: boolean;
  /** Whether the metric is poor */
  isPoor: boolean;
  /** Metric description */
  description?: string;
  /** Metric source */
  source?: 'web-vitals' | 'performance-api' | 'custom';
}

/**
 * Performance mark
 */
export interface PerformanceMark {
  /** Mark name */
  name: string;
  /** Mark timestamp */
  timestamp: number;
  /** Mark duration */
  duration?: number;
  /** Mark description */
  description?: string;
}

/**
 * Performance measure
 */
export interface PerformanceMeasure {
  /** Measure name */
  name: string;
  /** Measure start mark */
  startMark: string;
  /** Measure end mark */
  endMark: string;
  /** Measure duration */
  duration: number;
  /** Measure description */
  description?: string;
}

/**
 * Performance thresholds
 */
export interface PerformanceThresholds {
  /** Good threshold */
  good: number;
  /** Needs improvement threshold */
  needsImprovement: number;
  /** Poor threshold */
  poor: number;
}

/**
 * Default performance thresholds
 */
export const defaultPerformanceThresholds: Record<PerformanceMetricType, PerformanceThresholds> = {
  FCP: { good: 1800, needsImprovement: 3000, poor: 3000 },
  LCP: { good: 2500, needsImprovement: 4000, poor: 4000 },
  FID: { good: 100, needsImprovement: 300, poor: 300 },
  CLS: { good: 0.1, needsImprovement: 0.25, poor: 0.25 },
  TTI: { good: 3800, needsImprovement: 7300, poor: 7300 },
  TBT: { good: 200, needsImprovement: 600, poor: 600 },
  TTFB: { good: 800, needsImprovement: 1800, poor: 1800 },
  INP: { good: 200, needsImprovement: 500, poor: 500 },
  Custom: { good: 0, needsImprovement: 0, poor: 0 },
};

/**
 * Create a performance mark
 */
export function createPerformanceMark(
  name: string,
  description?: string
): PerformanceMark {
  // Create a performance mark using the Performance API
  const mark = performance.mark(name);
  
  return {
    name,
    timestamp: mark.startTime,
    description,
  };
}

/**
 * Create a performance measure
 */
export function createPerformanceMeasure(
  name: string,
  startMark: string,
  endMark: string,
  description?: string
): PerformanceMeasure {
  // Create a performance measure using the Performance API
  const measure = performance.measure(name, startMark, endMark);
  
  return {
    name,
    startMark,
    endMark,
    duration: measure.duration,
    description,
  };
}

/**
 * Create a performance metric
 */
export function createPerformanceMetric(
  name: string,
  type: PerformanceMetricType,
  value: number,
  unit: 'ms' | 's' | 'score' | 'count' | 'bytes' | 'percent' | 'custom',
  thresholds?: PerformanceThresholds,
  description?: string,
  source?: 'web-vitals' | 'performance-api' | 'custom'
): PerformanceMetric {
  // Use default thresholds if not provided
  const metricThresholds = thresholds || defaultPerformanceThresholds[type];
  
  // Determine if the metric is good, needs improvement, or poor
  const isGood = value <= metricThresholds.good;
  const needsImprovement = value > metricThresholds.good && value <= metricThresholds.needsImprovement;
  const isPoor = value > metricThresholds.poor;
  
  return {
    name,
    type,
    value,
    unit,
    timestamp: Date.now(),
    isGood,
    needsImprovement,
    isPoor,
    description,
    source,
  };
}

/**
 * Measure function execution time
 */
export function measureExecutionTime<T>(
  fn: () => T,
  name: string
): { result: T; duration: number } {
  // Create start mark
  const startMark = `${name}_start`;
  performance.mark(startMark);
  
  // Execute the function
  const result = fn();
  
  // Create end mark
  const endMark = `${name}_end`;
  performance.mark(endMark);
  
  // Create measure
  const measure = performance.measure(name, startMark, endMark);
  
  return {
    result,
    duration: measure.duration,
  };
}

/**
 * Measure async function execution time
 */
export async function measureAsyncExecutionTime<T>(
  fn: () => Promise<T>,
  name: string
): Promise<{ result: T; duration: number }> {
  // Create start mark
  const startMark = `${name}_start`;
  performance.mark(startMark);
  
  // Execute the function
  const result = await fn();
  
  // Create end mark
  const endMark = `${name}_end`;
  performance.mark(endMark);
  
  // Create measure
  const measure = performance.measure(name, startMark, endMark);
  
  return {
    result,
    duration: measure.duration,
  };
}

/**
 * Create a performance observer for web vitals
 */
export function createWebVitalsObserver(
  callback: (metric: PerformanceMetric) => void
): PerformanceObserver | undefined {
  // Check if PerformanceObserver is available
  if (typeof PerformanceObserver === 'undefined') {
    console.warn('PerformanceObserver is not available');
    return undefined;
  }
  
  try {
    // Create a performance observer for LCP
    const lcpObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const lastEntry = entries[entries.length - 1];
      
      if (lastEntry) {
        const lcp = createPerformanceMetric(
          'Largest Contentful Paint',
          'LCP',
          lastEntry.startTime,
          'ms',
          undefined,
          'Time when the largest content element becomes visible',
          'web-vitals'
        );
        
        callback(lcp);
      }
    });
    
    // Observe LCP entries
    lcpObserver.observe({ type: 'largest-contentful-paint', buffered: true });
    
    // Create a performance observer for FID
    const fidObserver = new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries();
      const firstEntry = entries[0];
      
      if (firstEntry) {
        const fid = createPerformanceMetric(
          'First Input Delay',
          'FID',
          (firstEntry as any).processingStart - (firstEntry as any).startTime,
          'ms',
          undefined,
          'Time from when a user first interacts to when the browser responds',
          'web-vitals'
        );
        
        callback(fid);
      }
    });
    
    // Observe FID entries
    fidObserver.observe({ type: 'first-input', buffered: true });
    
    // Create a performance observer for CLS
    const clsObserver = new PerformanceObserver((entryList) => {
      let cls = 0;
      
      entryList.getEntries().forEach((entry) => {
        if (!(entry as any).hadRecentInput) {
          cls += (entry as any).value;
        }
      });
      
      const clsMetric = createPerformanceMetric(
        'Cumulative Layout Shift',
        'CLS',
        cls,
        'score',
        undefined,
        'Sum of all layout shift scores that occur without user input',
        'web-vitals'
      );
      
      callback(clsMetric);
    });
    
    // Observe CLS entries
    clsObserver.observe({ type: 'layout-shift', buffered: true });
    
    // Return the observer for cleanup
    return lcpObserver;
  } catch (error) {
    console.error('Error creating web vitals observer:', error);
    return undefined;
  }
}

/**
 * Format performance metric value
 */
export function formatMetricValue(
  value: number,
  unit: 'ms' | 's' | 'score' | 'count' | 'bytes' | 'percent' | 'custom'
): string {
  switch (unit) {
    case 'ms':
      return `${value.toFixed(0)} ms`;
    case 's':
      return `${value.toFixed(2)} s`;
    case 'score':
      return value.toFixed(3);
    case 'count':
      return value.toFixed(0);
    case 'bytes':
      return formatBytes(value);
    case 'percent':
      return `${value.toFixed(1)}%`;
    case 'custom':
    default:
      return value.toString();
  }
}

/**
 * Format bytes to human-readable format
 */
export function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}

export default {
  createPerformanceMark,
  createPerformanceMeasure,
  createPerformanceMetric,
  measureExecutionTime,
  measureAsyncExecutionTime,
  createWebVitalsObserver,
  formatMetricValue,
  formatBytes,
};
