'use client';

/**
 * PageLayout Component
 *
 * This component provides a responsive page layout
 * following iOS 19 design principles.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import MobileNavigation from './MobileNavigation';
import ResponsiveContainer from './ResponsiveContainer';

interface PageLayoutProps {
  /** Children components */
  children: React.ReactNode;
  /** Page title */
  title?: string;
  /** Page description */
  description?: string;
  /** Additional class names */
  className?: string;
  /** Whether to show navigation */
  showNavigation?: boolean;
  /** Whether to use a container */
  useContainer?: boolean;
  /** Container max width */
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';
  /** Container padding */
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Whether to use glass effect */
  glass?: boolean;
  /** Whether to show a border */
  bordered?: boolean;
  /** Whether to show a shadow */
  shadowed?: boolean;
  /** Whether to show rounded corners */
  rounded?: boolean;
  /** Whether to use full height */
  fullHeight?: boolean;
  /** Whether to show a header */
  showHeader?: boolean;
  /** Whether to show a footer */
  showFooter?: boolean;
  /** Custom navigation items */
  navItems?: Array<{
    href: string;
    label: string;
    icon?: React.ReactNode;
  }>;
}

/**
 * A responsive page layout component that follows iOS 19 design principles
 */
export function PageLayout({
  children,
  title,
  description,
  className,
  showNavigation = true,
  useContainer = true,
  maxWidth = 'xl',
  padding = 'md',
  glass = false,
  bordered = false,
  shadowed = false,
  rounded = false,
  fullHeight = true,
  showHeader = true,
  showFooter = false,
  navItems,
}: PageLayoutProps) {
  return (
    <div className={cn('min-h-screen flex flex-col', className)}>
      {/* Mobile navigation */}
      {showNavigation && (
        <MobileNavigation
          title={title}
          navItems={navItems}
          bordered={true}
          shadowed={true}
          glass={true}
          sticky={true}
        />
      )}

      {/* Main content */}
      <main className={cn('flex-1', fullHeight && 'flex flex-col')}>
        {/* Header */}
        {showHeader && title && (
          <header className="py-4 sm:py-5 md:py-6 px-4 sm:px-6 md:px-8">
            <div className={cn(useContainer && 'container mx-auto')}>
              <h1 className="text-2xl font-bold">{title}</h1>
              {description && (
                <p className="text-muted-foreground mt-2 sm:mt-2.5">{description}</p>
              )}
            </div>
          </header>
        )}

        {/* Content */}
        {useContainer ? (
          <ResponsiveContainer
            maxWidth={maxWidth}
            padding={padding}
            glass={glass}
            bordered={bordered}
            shadowed={shadowed}
            rounded={rounded}
            fullHeight={fullHeight}
            className="flex-1"
          >
            {children}
          </ResponsiveContainer>
        ) : (
          <div className={cn('flex-1', fullHeight && 'flex flex-col')}>
            {children}
          </div>
        )}

        {/* Footer */}
        {showFooter && (
          <footer className="py-4 px-4 sm:px-6 md:px-8 border-t border-border/20 mt-auto">
            <div className={cn(useContainer && 'container mx-auto')}>
              <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
                <p className="text-sm text-muted-foreground">
                  © {new Date().getFullYear()} Weather Route
                </p>
                <div className="flex items-center gap-4">
                  <a
                    href="#"
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Privacy Policy
                  </a>
                  <a
                    href="#"
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Terms of Service
                  </a>
                </div>
              </div>
            </div>
          </footer>
        )}
      </main>
    </div>
  );
}

export default PageLayout;
