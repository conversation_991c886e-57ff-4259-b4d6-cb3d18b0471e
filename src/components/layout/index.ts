/**
 * Layout Components
 *
 * This file exports all layout components for easy imports.
 */

// Export existing layout components
export * from './header';
export * from './footer';
export * from './page-wrapper';
export * from './responsive-layout';

// Export new responsive layout components
export { ResponsiveContainer } from './ResponsiveContainer';
export { ResponsiveGrid } from './ResponsiveGrid';
export { ResponsiveSection } from './ResponsiveSection';
export { MobileNavigation } from './MobileNavigation';
export { BottomNavigation } from './BottomNavigation';
export { PageLayout } from './PageLayout';
export { DashboardLayout } from './DashboardLayout';
export { CardGrid } from './CardGrid';
export { ResponsiveLayoutWrapper } from './ResponsiveLayoutWrapper';
export { GridSection } from './GridSection';

// Export types
export type { ResponsiveContainerProps } from './ResponsiveContainer';
export type { ResponsiveGridProps } from './ResponsiveGrid';
export type { ResponsiveSectionProps } from './ResponsiveSection';
export type { ResponsiveLayoutWrapperProps, NavItem } from './ResponsiveLayoutWrapper';
export type { CardGridProps, CardItem } from './CardGrid';
export type { GridSectionProps } from './GridSection';
export type { PageLayoutProps } from './PageLayout';
