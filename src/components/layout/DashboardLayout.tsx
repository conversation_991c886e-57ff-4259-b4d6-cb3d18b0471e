'use client';

/**
 * DashboardLayout Component
 * 
 * This component provides a responsive dashboard layout
 * following iOS 19 design principles.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import MobileNavigation from './MobileNavigation';
import ResponsiveGrid from './ResponsiveGrid';

interface DashboardLayoutProps {
  /** Children components */
  children: React.ReactNode;
  /** Dashboard title */
  title?: string;
  /** Dashboard description */
  description?: string;
  /** Additional class names */
  className?: string;
  /** Whether to show navigation */
  showNavigation?: boolean;
  /** Whether to use a grid layout */
  useGrid?: boolean;
  /** Number of columns on mobile (default: 1) */
  mobileColumns?: 1 | 2;
  /** Number of columns on tablet (default: 2) */
  tabletColumns?: 1 | 2 | 3;
  /** Number of columns on desktop (default: 3) */
  desktopColumns?: 1 | 2 | 3 | 4;
  /** Number of columns on large desktop (default: 4) */
  largeDesktopColumns?: 1 | 2 | 3 | 4 | 5 | 6;
  /** Gap between items (default: 'md') */
  gap?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Container padding */
  padding?: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  /** Whether to use glass effect */
  glass?: boolean;
  /** Whether to show a border */
  bordered?: boolean;
  /** Whether to show a shadow */
  shadowed?: boolean;
  /** Whether to show rounded corners */
  rounded?: boolean;
  /** Whether to use full height */
  fullHeight?: boolean;
  /** Whether to show a header */
  showHeader?: boolean;
  /** Whether to show a footer */
  showFooter?: boolean;
  /** Custom navigation items */
  navItems?: Array<{
    href: string;
    label: string;
    icon?: React.ReactNode;
  }>;
}

/**
 * A responsive dashboard layout component that follows iOS 19 design principles
 */
export function DashboardLayout({
  children,
  title,
  description,
  className,
  showNavigation = true,
  useGrid = true,
  mobileColumns = 1,
  tabletColumns = 2,
  desktopColumns = 3,
  largeDesktopColumns = 4,
  gap = 'md',
  padding = 'md',
  glass = false,
  bordered = false,
  shadowed = false,
  rounded = false,
  fullHeight = true,
  showHeader = true,
  showFooter = false,
  navItems,
}: DashboardLayoutProps) {
  // Padding classes
  const paddingClasses = {
    none: 'p-0',
    xs: 'p-2',
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6',
    xl: 'p-8',
  };

  return (
    <div className={cn('min-h-screen flex flex-col', className)}>
      {/* Mobile navigation */}
      {showNavigation && (
        <MobileNavigation
          title={title}
          navItems={navItems}
          bordered={true}
          shadowed={true}
          glass={true}
          sticky={true}
        />
      )}

      {/* Main content */}
      <main className={cn('flex-1', fullHeight && 'flex flex-col')}>
        {/* Header */}
        {showHeader && title && (
          <header className="py-4 px-4 sm:px-6 md:px-8">
            <div className="container mx-auto">
              <h1 className="text-2xl font-bold">{title}</h1>
              {description && (
                <p className="text-muted-foreground mt-1">{description}</p>
              )}
            </div>
          </header>
        )}

        {/* Content */}
        <div
          className={cn(
            'flex-1',
            paddingClasses[padding],
            glass && 'bg-white/50 dark:bg-card/50 backdrop-blur-sm',
            bordered && 'border border-border/20',
            shadowed && 'shadow-sm',
            rounded && 'rounded-xl',
            fullHeight && 'flex flex-col'
          )}
        >
          {useGrid ? (
            <ResponsiveGrid
              mobileColumns={mobileColumns}
              tabletColumns={tabletColumns}
              desktopColumns={desktopColumns}
              largeDesktopColumns={largeDesktopColumns}
              gap={gap}
              className="flex-1"
            >
              {children}
            </ResponsiveGrid>
          ) : (
            <div className={cn('flex-1', fullHeight && 'flex flex-col')}>
              {children}
            </div>
          )}
        </div>

        {/* Footer */}
        {showFooter && (
          <footer className="py-4 px-4 sm:px-6 md:px-8 border-t border-border/20 mt-auto">
            <div className="container mx-auto">
              <div className="flex flex-col sm:flex-row justify-between items-center gap-2">
                <p className="text-sm text-muted-foreground">
                  © {new Date().getFullYear()} Weather Route
                </p>
                <div className="flex items-center gap-4">
                  <a
                    href="#"
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Privacy Policy
                  </a>
                  <a
                    href="#"
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    Terms of Service
                  </a>
                </div>
              </div>
            </div>
          </footer>
        )}
      </main>
    </div>
  );
}

export default DashboardLayout;
