'use client';

/**
 * PerformanceMonitor Component
 * 
 * This component monitors and displays performance metrics for the application.
 */

import React, { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { 
  <PERSON>ap, 
  Clock, 
  <PERSON><PERSON><PERSON>, 
  Cpu, 
  Memory, 
  Wifi, 
  Battery, 
  X,
  ChevronUp,
  ChevronDown,
  RefreshCw
} from 'lucide-react';

interface PerformanceMetrics {
  /** First Contentful Paint (ms) */
  fcp: number | null;
  /** Largest Contentful Paint (ms) */
  lcp: number | null;
  /** First Input Delay (ms) */
  fid: number | null;
  /** Cumulative Layout Shift */
  cls: number | null;
  /** Time to Interactive (ms) */
  tti: number | null;
  /** Total Blocking Time (ms) */
  tbt: number | null;
  /** Memory usage (MB) */
  memory: number | null;
  /** CPU usage (%) */
  cpu: number | null;
  /** Network information */
  network: {
    /** Effective connection type */
    effectiveType: string;
    /** Downlink speed (Mbps) */
    downlink: number | null;
    /** Round-trip time (ms) */
    rtt: number | null;
    /** Whether data saver is enabled */
    saveData: boolean | null;
  };
  /** Battery information */
  battery: {
    /** Battery level (0-1) */
    level: number | null;
    /** Whether the device is charging */
    charging: boolean | null;
    /** Estimated time to full charge (seconds) */
    chargingTime: number | null;
    /** Estimated time to discharge (seconds) */
    dischargingTime: number | null;
  };
}

interface PerformanceMonitorProps {
  /** Whether to show the monitor by default */
  defaultVisible?: boolean;
  /** Whether to show the monitor in development mode only */
  devOnly?: boolean;
  /** Position of the monitor */
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  /** Additional class names */
  className?: string;
}

/**
 * A component that monitors and displays performance metrics for the application
 */
export function PerformanceMonitor({
  defaultVisible = false,
  devOnly = true,
  position = 'bottom-right',
  className,
}: PerformanceMonitorProps) {
  const [isVisible, setIsVisible] = useState(defaultVisible);
  const [isExpanded, setIsExpanded] = useState(true);
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fcp: null,
    lcp: null,
    fid: null,
    cls: null,
    tti: null,
    tbt: null,
    memory: null,
    cpu: null,
    network: {
      effectiveType: 'unknown',
      downlink: null,
      rtt: null,
      saveData: null,
    },
    battery: {
      level: null,
      charging: null,
      chargingTime: null,
      dischargingTime: null,
    },
  });
  
  const rafRef = useRef<number | null>(null);
  
  // Check if we should show the monitor
  const shouldShow = !devOnly || process.env.NODE_ENV === 'development';
  
  // Position classes
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
  };
  
  // Collect performance metrics
  useEffect(() => {
    if (!shouldShow || !isVisible) return;
    
    // Collect Web Vitals
    const collectWebVitals = () => {
      // First Contentful Paint
      const paintEntries = performance.getEntriesByType('paint');
      const fcpEntry = paintEntries.find(entry => entry.name === 'first-contentful-paint');
      
      // Largest Contentful Paint
      let lcpValue = null;
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const lastEntry = entries[entries.length - 1];
        lcpValue = lastEntry ? lastEntry.startTime : null;
        
        setMetrics(prev => ({
          ...prev,
          lcp: lcpValue,
        }));
      }).observe({ type: 'largest-contentful-paint', buffered: true });
      
      // First Input Delay
      let fidValue = null;
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        const firstInput = entries[0];
        fidValue = firstInput ? firstInput.processingStart - firstInput.startTime : null;
        
        setMetrics(prev => ({
          ...prev,
          fid: fidValue,
        }));
      }).observe({ type: 'first-input', buffered: true });
      
      // Cumulative Layout Shift
      let clsValue = 0;
      new PerformanceObserver((entryList) => {
        const entries = entryList.getEntries();
        entries.forEach(entry => {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        });
        
        setMetrics(prev => ({
          ...prev,
          cls: clsValue,
        }));
      }).observe({ type: 'layout-shift', buffered: true });
      
      // Update FCP
      setMetrics(prev => ({
        ...prev,
        fcp: fcpEntry ? fcpEntry.startTime : null,
      }));
    };
    
    // Collect system metrics
    const collectSystemMetrics = () => {
      // Memory usage
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        const memoryUsage = memory ? Math.round(memory.usedJSHeapSize / (1024 * 1024)) : null;
        
        setMetrics(prev => ({
          ...prev,
          memory: memoryUsage,
        }));
      }
      
      // Network information
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        
        setMetrics(prev => ({
          ...prev,
          network: {
            effectiveType: connection.effectiveType,
            downlink: connection.downlink,
            rtt: connection.rtt,
            saveData: connection.saveData,
          },
        }));
      }
      
      // Battery information
      if ('getBattery' in navigator) {
        (navigator as any).getBattery().then((battery: any) => {
          setMetrics(prev => ({
            ...prev,
            battery: {
              level: battery.level,
              charging: battery.charging,
              chargingTime: battery.chargingTime,
              dischargingTime: battery.dischargingTime,
            },
          }));
          
          // Listen for battery changes
          battery.addEventListener('levelchange', collectSystemMetrics);
          battery.addEventListener('chargingchange', collectSystemMetrics);
          
          return () => {
            battery.removeEventListener('levelchange', collectSystemMetrics);
            battery.removeEventListener('chargingchange', collectSystemMetrics);
          };
        });
      }
      
      // Schedule next collection
      rafRef.current = requestAnimationFrame(collectSystemMetrics);
    };
    
    // Initial collection
    collectWebVitals();
    collectSystemMetrics();
    
    // Clean up
    return () => {
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [shouldShow, isVisible]);
  
  // Format time in ms
  const formatTime = (time: number | null) => {
    if (time === null) return 'N/A';
    return `${time.toFixed(2)} ms`;
  };
  
  // Format size in MB
  const formatSize = (size: number | null) => {
    if (size === null) return 'N/A';
    return `${size.toFixed(2)} MB`;
  };
  
  // Format percentage
  const formatPercentage = (value: number | null) => {
    if (value === null) return 'N/A';
    return `${(value * 100).toFixed(2)}%`;
  };
  
  // Get status color based on value
  const getStatusColor = (value: number | null, thresholds: { good: number; medium: number }) => {
    if (value === null) return 'text-muted-foreground';
    if (value <= thresholds.good) return 'text-green-500';
    if (value <= thresholds.medium) return 'text-amber-500';
    return 'text-red-500';
  };
  
  // Refresh metrics
  const refreshMetrics = () => {
    // Clear performance entries
    performance.clearMarks();
    performance.clearMeasures();
    performance.clearResourceTimings();
    
    // Reset metrics
    setMetrics({
      fcp: null,
      lcp: null,
      fid: null,
      cls: null,
      tti: null,
      tbt: null,
      memory: null,
      cpu: null,
      network: {
        effectiveType: 'unknown',
        downlink: null,
        rtt: null,
        saveData: null,
      },
      battery: {
        level: null,
        charging: null,
        chargingTime: null,
        dischargingTime: null,
      },
    });
    
    // Force a layout to trigger new measurements
    document.body.getBoundingClientRect();
  };
  
  if (!shouldShow) return null;
  
  return (
    <>
      {/* Toggle button */}
      <Button
        variant="outline"
        size="icon"
        className={cn(
          'fixed z-50 h-8 w-8 rounded-full bg-card/80 backdrop-blur-sm shadow-md border border-border/20',
          positionClasses[position],
          isVisible && 'hidden'
        )}
        onClick={() => setIsVisible(true)}
      >
        <Zap className="h-4 w-4" />
      </Button>
      
      {/* Performance monitor */}
      {isVisible && (
        <Card
          className={cn(
            'fixed z-50 w-80 shadow-lg',
            positionClasses[position],
            className
          )}
        >
          <CardHeader className="p-3 flex flex-row items-center justify-between space-y-0">
            <CardTitle className="text-sm font-medium flex items-center">
              <Zap className="h-4 w-4 mr-2" />
              Performance Monitor
            </CardTitle>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={refreshMetrics}
              >
                <RefreshCw className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setIsExpanded(!isExpanded)}
              >
                {isExpanded ? (
                  <ChevronUp className="h-3 w-3" />
                ) : (
                  <ChevronDown className="h-3 w-3" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-6 w-6"
                onClick={() => setIsVisible(false)}
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </CardHeader>
          
          {isExpanded && (
            <CardContent className="p-3 pt-0">
              <div className="space-y-3 text-xs">
                {/* Web Vitals */}
                <div className="space-y-1">
                  <h3 className="font-medium flex items-center">
                    <BarChart className="h-3 w-3 mr-1" />
                    Web Vitals
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex justify-between">
                      <span>FCP:</span>
                      <span className={getStatusColor(metrics.fcp, { good: 1000, medium: 2500 })}>
                        {metrics.fcp ? formatTime(metrics.fcp) : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>LCP:</span>
                      <span className={getStatusColor(metrics.lcp, { good: 2500, medium: 4000 })}>
                        {metrics.lcp ? formatTime(metrics.lcp) : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>FID:</span>
                      <span className={getStatusColor(metrics.fid, { good: 100, medium: 300 })}>
                        {metrics.fid ? formatTime(metrics.fid) : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>CLS:</span>
                      <span className={getStatusColor(metrics.cls, { good: 0.1, medium: 0.25 })}>
                        {metrics.cls !== null ? metrics.cls.toFixed(3) : 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* System Resources */}
                <div className="space-y-1">
                  <h3 className="font-medium flex items-center">
                    <Cpu className="h-3 w-3 mr-1" />
                    System Resources
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex justify-between">
                      <span>Memory:</span>
                      <span>
                        {metrics.memory ? formatSize(metrics.memory) : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>CPU:</span>
                      <span>
                        {metrics.cpu ? formatPercentage(metrics.cpu) : 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Network */}
                <div className="space-y-1">
                  <h3 className="font-medium flex items-center">
                    <Wifi className="h-3 w-3 mr-1" />
                    Network
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex justify-between">
                      <span>Type:</span>
                      <span>
                        {metrics.network.effectiveType}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Downlink:</span>
                      <span>
                        {metrics.network.downlink ? `${metrics.network.downlink} Mbps` : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>RTT:</span>
                      <span>
                        {metrics.network.rtt ? `${metrics.network.rtt} ms` : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Data Saver:</span>
                      <span>
                        {metrics.network.saveData !== null ? (metrics.network.saveData ? 'On' : 'Off') : 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
                
                {/* Battery */}
                <div className="space-y-1">
                  <h3 className="font-medium flex items-center">
                    <Battery className="h-3 w-3 mr-1" />
                    Battery
                  </h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex justify-between">
                      <span>Level:</span>
                      <span>
                        {metrics.battery.level !== null ? `${(metrics.battery.level * 100).toFixed(0)}%` : 'N/A'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Charging:</span>
                      <span>
                        {metrics.battery.charging !== null ? (metrics.battery.charging ? 'Yes' : 'No') : 'N/A'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          )}
        </Card>
      )}
    </>
  );
}

export default PerformanceMonitor;
