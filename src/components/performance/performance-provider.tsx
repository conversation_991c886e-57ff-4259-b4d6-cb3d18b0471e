'use client';

/**
 * PerformanceProvider Component
 * 
 * This component provides performance optimization context and features
 * for the application.
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useDevicePerformance } from '@/hooks/use-performance';
import PerformanceMonitor from './performance-monitor';

interface PerformanceContextType {
  /** Whether to reduce animations */
  reduceAnimations: boolean;
  /** Whether to reduce image quality */
  reduceImageQuality: boolean;
  /** Whether to reduce the number of items rendered */
  reduceItemCount: boolean;
  /** Whether to disable background effects */
  disableBackgroundEffects: boolean;
  /** Whether to use simpler layouts */
  useSimpleLayout: boolean;
  /** Whether to lazy load content */
  lazyLoadContent: boolean;
  /** Whether to reduce map quality */
  reduceMapQuality: boolean;
  /** Whether the device is a mobile device */
  isMobile: boolean;
  /** Whether the device is a tablet device */
  isTablet: boolean;
  /** Whether the device is a desktop device */
  isDesktop: boolean;
  /** Whether the device is in portrait orientation */
  isPortrait: boolean;
  /** Whether the device is in landscape orientation */
  isLandscape: boolean;
  /** Whether the device has a touch screen */
  hasTouch: boolean;
  /** Whether the device has a mouse */
  hasMouse: boolean;
  /** Whether the device has a keyboard */
  hasKeyboard: boolean;
  /** Whether the device is online */
  isOnline: boolean;
  /** Whether the device is offline */
  isOffline: boolean;
  /** Whether the device is in low power mode */
  isLowPower: boolean;
  /** Whether the device has reduced motion enabled */
  hasReducedMotion: boolean;
  /** Whether the device has a high contrast mode enabled */
  hasHighContrast: boolean;
  /** Whether the device has a dark mode enabled */
  isDarkMode: boolean;
  /** Whether the device has a light mode enabled */
  isLightMode: boolean;
  /** Whether the device has a data saver mode enabled */
  isDataSaver: boolean;
  /** Whether the device has a slow connection */
  isSlowConnection: boolean;
  /** Whether the device has a fast connection */
  isFastConnection: boolean;
  /** Whether the device is a low-end device */
  isLowEndDevice: boolean;
  /** Whether the device is a high-end device */
  isHighEndDevice: boolean;
  /** Whether the device has low memory */
  isLowMemory: boolean;
  /** Whether the device has high memory */
  isHighMemory: boolean;
  /** Whether the device has a low battery */
  isLowBattery: boolean;
  /** Whether the device is charging */
  isCharging: boolean;
  /** Whether the device is in a power saving mode */
  isPowerSaving: boolean;
  /** Whether the device is in a high performance mode */
  isHighPerformance: boolean;
  /** Whether the device is in a balanced mode */
  isBalanced: boolean;
  /** Whether the device is in a low performance mode */
  isLowPerformance: boolean;
  /** Whether the device is in a high quality mode */
  isHighQuality: boolean;
  /** Whether the device is in a low quality mode */
  isLowQuality: boolean;
  /** Whether the device is in a balanced quality mode */
  isBalancedQuality: boolean;
  /** Whether the device is in a high speed mode */
  isHighSpeed: boolean;
  /** Whether the device is in a low speed mode */
  isLowSpeed: boolean;
  /** Whether the device is in a balanced speed mode */
  isBalancedSpeed: boolean;
  /** Whether the device is in a high battery mode */
  isHighBattery: boolean;
  /** Whether the device is in a low battery mode */
  isLowBatteryMode: boolean;
  /** Whether the device is in a balanced battery mode */
  isBalancedBattery: boolean;
  /** Whether the device is in a high memory mode */
  isHighMemoryMode: boolean;
  /** Whether the device is in a low memory mode */
  isLowMemoryMode: boolean;
  /** Whether the device is in a balanced memory mode */
  isBalancedMemory: boolean;
  /** Whether the device is in a high CPU mode */
  isHighCPU: boolean;
  /** Whether the device is in a low CPU mode */
  isLowCPU: boolean;
  /** Whether the device is in a balanced CPU mode */
  isBalancedCPU: boolean;
  /** Whether the device is in a high GPU mode */
  isHighGPU: boolean;
  /** Whether the device is in a low GPU mode */
  isLowGPU: boolean;
  /** Whether the device is in a balanced GPU mode */
  isBalancedGPU: boolean;
  /** Whether the device is in a high network mode */
  isHighNetwork: boolean;
  /** Whether the device is in a low network mode */
  isLowNetwork: boolean;
  /** Whether the device is in a balanced network mode */
  isBalancedNetwork: boolean;
  /** Whether the device is in a high storage mode */
  isHighStorage: boolean;
  /** Whether the device is in a low storage mode */
  isLowStorage: boolean;
  /** Whether the device is in a balanced storage mode */
  isBalancedStorage: boolean;
  /** Whether the device is in a high screen mode */
  isHighScreen: boolean;
  /** Whether the device is in a low screen mode */
  isLowScreen: boolean;
  /** Whether the device is in a balanced screen mode */
  isBalancedScreen: boolean;
  /** Whether the device is in a high audio mode */
  isHighAudio: boolean;
  /** Whether the device is in a low audio mode */
  isLowAudio: boolean;
  /** Whether the device is in a balanced audio mode */
  isBalancedAudio: boolean;
  /** Whether the device is in a high video mode */
  isHighVideo: boolean;
  /** Whether the device is in a low video mode */
  isLowVideo: boolean;
  /** Whether the device is in a balanced video mode */
  isBalancedVideo: boolean;
  /** Whether the device is in a high image mode */
  isHighImage: boolean;
  /** Whether the device is in a low image mode */
  isLowImage: boolean;
  /** Whether the device is in a balanced image mode */
  isBalancedImage: boolean;
  /** Whether the device is in a high text mode */
  isHighText: boolean;
  /** Whether the device is in a low text mode */
  isLowText: boolean;
  /** Whether the device is in a balanced text mode */
  isBalancedText: boolean;
  /** Whether the device is in a high UI mode */
  isHighUI: boolean;
  /** Whether the device is in a low UI mode */
  isLowUI: boolean;
  /** Whether the device is in a balanced UI mode */
  isBalancedUI: boolean;
  /** Whether the device is in a high UX mode */
  isHighUX: boolean;
  /** Whether the device is in a low UX mode */
  isLowUX: boolean;
  /** Whether the device is in a balanced UX mode */
  isBalancedUX: boolean;
  /** Whether the device is in a high accessibility mode */
  isHighAccessibility: boolean;
  /** Whether the device is in a low accessibility mode */
  isLowAccessibility: boolean;
  /** Whether the device is in a balanced accessibility mode */
  isBalancedAccessibility: boolean;
  /** Whether the device is in a high security mode */
  isHighSecurity: boolean;
  /** Whether the device is in a low security mode */
  isLowSecurity: boolean;
  /** Whether the device is in a balanced security mode */
  isBalancedSecurity: boolean;
  /** Whether the device is in a high privacy mode */
  isHighPrivacy: boolean;
  /** Whether the device is in a low privacy mode */
  isLowPrivacy: boolean;
  /** Whether the device is in a balanced privacy mode */
  isBalancedPrivacy: boolean;
}

// Create context with default values
const PerformanceContext = createContext<PerformanceContextType>({
  reduceAnimations: false,
  reduceImageQuality: false,
  reduceItemCount: false,
  disableBackgroundEffects: false,
  useSimpleLayout: false,
  lazyLoadContent: true,
  reduceMapQuality: false,
  isMobile: false,
  isTablet: false,
  isDesktop: true,
  isPortrait: false,
  isLandscape: true,
  hasTouch: false,
  hasMouse: true,
  hasKeyboard: true,
  isOnline: true,
  isOffline: false,
  isLowPower: false,
  hasReducedMotion: false,
  hasHighContrast: false,
  isDarkMode: false,
  isLightMode: true,
  isDataSaver: false,
  isSlowConnection: false,
  isFastConnection: true,
  isLowEndDevice: false,
  isHighEndDevice: true,
  isLowMemory: false,
  isHighMemory: true,
  isLowBattery: false,
  isCharging: true,
  isPowerSaving: false,
  isHighPerformance: true,
  isBalanced: false,
  isLowPerformance: false,
  isHighQuality: true,
  isLowQuality: false,
  isBalancedQuality: false,
  isHighSpeed: true,
  isLowSpeed: false,
  isBalancedSpeed: false,
  isHighBattery: true,
  isLowBatteryMode: false,
  isBalancedBattery: false,
  isHighMemoryMode: true,
  isLowMemoryMode: false,
  isBalancedMemory: false,
  isHighCPU: true,
  isLowCPU: false,
  isBalancedCPU: false,
  isHighGPU: true,
  isLowGPU: false,
  isBalancedGPU: false,
  isHighNetwork: true,
  isLowNetwork: false,
  isBalancedNetwork: false,
  isHighStorage: true,
  isLowStorage: false,
  isBalancedStorage: false,
  isHighScreen: true,
  isLowScreen: false,
  isBalancedScreen: false,
  isHighAudio: true,
  isLowAudio: false,
  isBalancedAudio: false,
  isHighVideo: true,
  isLowVideo: false,
  isBalancedVideo: false,
  isHighImage: true,
  isLowImage: false,
  isBalancedImage: false,
  isHighText: true,
  isLowText: false,
  isBalancedText: false,
  isHighUI: true,
  isLowUI: false,
  isBalancedUI: false,
  isHighUX: true,
  isLowUX: false,
  isBalancedUX: false,
  isHighAccessibility: true,
  isLowAccessibility: false,
  isBalancedAccessibility: false,
  isHighSecurity: true,
  isLowSecurity: false,
  isBalancedSecurity: false,
  isHighPrivacy: true,
  isLowPrivacy: false,
  isBalancedPrivacy: false,
});

interface PerformanceProviderProps {
  /** Children components */
  children: React.ReactNode;
  /** Whether to show the performance monitor */
  showMonitor?: boolean;
  /** Whether to show the monitor in development mode only */
  monitorDevOnly?: boolean;
  /** Position of the monitor */
  monitorPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

/**
 * A provider component that provides performance optimization context and features
 */
export function PerformanceProvider({
  children,
  showMonitor = false,
  monitorDevOnly = true,
  monitorPosition = 'bottom-right',
}: PerformanceProviderProps) {
  const devicePerformance = useDevicePerformance();
  const [isOnline, setIsOnline] = useState(true);
  const [isDarkMode, setIsDarkMode] = useState(false);
  
  // Check if the device is online
  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    setIsOnline(navigator.onLine);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);
  
  // Check if the device is in dark mode
  useEffect(() => {
    const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    const handleDarkModeChange = (e: MediaQueryListEvent) => {
      setIsDarkMode(e.matches);
    };
    
    setIsDarkMode(darkModeMediaQuery.matches);
    
    darkModeMediaQuery.addEventListener('change', handleDarkModeChange);
    
    return () => {
      darkModeMediaQuery.removeEventListener('change', handleDarkModeChange);
    };
  }, []);
  
  // Determine device type
  const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
  const isTablet = typeof window !== 'undefined' && window.innerWidth >= 768 && window.innerWidth < 1024;
  const isDesktop = typeof window !== 'undefined' && window.innerWidth >= 1024;
  
  // Determine orientation
  const isPortrait = typeof window !== 'undefined' && window.innerHeight > window.innerWidth;
  const isLandscape = typeof window !== 'undefined' && window.innerWidth >= window.innerHeight;
  
  // Determine input methods
  const hasTouch = typeof window !== 'undefined' && ('ontouchstart' in window || navigator.maxTouchPoints > 0);
  const hasMouse = typeof window !== 'undefined' && matchMedia('(pointer: fine)').matches;
  const hasKeyboard = typeof window !== 'undefined' && matchMedia('(hover: hover)').matches;
  
  // Create context value
  const contextValue: PerformanceContextType = {
    ...devicePerformance,
    isMobile,
    isTablet,
    isDesktop,
    isPortrait,
    isLandscape,
    hasTouch,
    hasMouse,
    hasKeyboard,
    isOnline,
    isOffline: !isOnline,
    isLowPower: devicePerformance.isBatteryLow,
    hasReducedMotion: devicePerformance.isReducedMotion,
    hasHighContrast: false, // Not implemented yet
    isDarkMode,
    isLightMode: !isDarkMode,
    isDataSaver: devicePerformance.isDataSaverEnabled,
    isSlowConnection: devicePerformance.effectiveConnectionType === '2g' || devicePerformance.effectiveConnectionType === 'slow-2g',
    isFastConnection: devicePerformance.effectiveConnectionType === '4g',
    isLowEndDevice: devicePerformance.isLowEndDevice,
    isHighEndDevice: !devicePerformance.isLowEndDevice,
    isLowMemory: devicePerformance.isLowMemory,
    isHighMemory: !devicePerformance.isLowMemory,
    isLowBattery: devicePerformance.isBatteryLow,
    isCharging: false, // Not implemented yet
    isPowerSaving: devicePerformance.isBatteryLow,
    isHighPerformance: !devicePerformance.isLowEndDevice && !devicePerformance.isLowMemory && !devicePerformance.isBatteryLow,
    isBalanced: !devicePerformance.isLowEndDevice && !devicePerformance.isLowMemory && !devicePerformance.isBatteryLow,
    isLowPerformance: devicePerformance.isLowEndDevice || devicePerformance.isLowMemory || devicePerformance.isBatteryLow,
    isHighQuality: !devicePerformance.shouldReduceImageQuality,
    isLowQuality: devicePerformance.shouldReduceImageQuality,
    isBalancedQuality: !devicePerformance.shouldReduceImageQuality,
    isHighSpeed: !devicePerformance.isLowEndDevice,
    isLowSpeed: devicePerformance.isLowEndDevice,
    isBalancedSpeed: !devicePerformance.isLowEndDevice,
    isHighBattery: !devicePerformance.isBatteryLow,
    isLowBatteryMode: devicePerformance.isBatteryLow,
    isBalancedBattery: !devicePerformance.isBatteryLow,
    isHighMemoryMode: !devicePerformance.isLowMemory,
    isLowMemoryMode: devicePerformance.isLowMemory,
    isBalancedMemory: !devicePerformance.isLowMemory,
    isHighCPU: !devicePerformance.isLowEndDevice,
    isLowCPU: devicePerformance.isLowEndDevice,
    isBalancedCPU: !devicePerformance.isLowEndDevice,
    isHighGPU: !devicePerformance.isLowEndDevice,
    isLowGPU: devicePerformance.isLowEndDevice,
    isBalancedGPU: !devicePerformance.isLowEndDevice,
    isHighNetwork: !devicePerformance.isDataSaverEnabled && devicePerformance.effectiveConnectionType === '4g',
    isLowNetwork: devicePerformance.isDataSaverEnabled || devicePerformance.effectiveConnectionType === '2g' || devicePerformance.effectiveConnectionType === 'slow-2g',
    isBalancedNetwork: !devicePerformance.isDataSaverEnabled && devicePerformance.effectiveConnectionType === '3g',
    isHighStorage: true, // Not implemented yet
    isLowStorage: false, // Not implemented yet
    isBalancedStorage: true, // Not implemented yet
    isHighScreen: !isMobile,
    isLowScreen: isMobile,
    isBalancedScreen: isTablet,
    isHighAudio: true, // Not implemented yet
    isLowAudio: false, // Not implemented yet
    isBalancedAudio: true, // Not implemented yet
    isHighVideo: !devicePerformance.shouldReduceImageQuality,
    isLowVideo: devicePerformance.shouldReduceImageQuality,
    isBalancedVideo: !devicePerformance.shouldReduceImageQuality,
    isHighImage: !devicePerformance.shouldReduceImageQuality,
    isLowImage: devicePerformance.shouldReduceImageQuality,
    isBalancedImage: !devicePerformance.shouldReduceImageQuality,
    isHighText: true, // Not implemented yet
    isLowText: false, // Not implemented yet
    isBalancedText: true, // Not implemented yet
    isHighUI: !devicePerformance.shouldUseSimpleLayout,
    isLowUI: devicePerformance.shouldUseSimpleLayout,
    isBalancedUI: !devicePerformance.shouldUseSimpleLayout,
    isHighUX: !devicePerformance.shouldReduceAnimations,
    isLowUX: devicePerformance.shouldReduceAnimations,
    isBalancedUX: !devicePerformance.shouldReduceAnimations,
    isHighAccessibility: true, // Not implemented yet
    isLowAccessibility: false, // Not implemented yet
    isBalancedAccessibility: true, // Not implemented yet
    isHighSecurity: true, // Not implemented yet
    isLowSecurity: false, // Not implemented yet
    isBalancedSecurity: true, // Not implemented yet
    isHighPrivacy: true, // Not implemented yet
    isLowPrivacy: false, // Not implemented yet
    isBalancedPrivacy: true, // Not implemented yet
  };
  
  return (
    <PerformanceContext.Provider value={contextValue}>
      {children}
      {showMonitor && (
        <PerformanceMonitor
          defaultVisible={false}
          devOnly={monitorDevOnly}
          position={monitorPosition}
        />
      )}
    </PerformanceContext.Provider>
  );
}

/**
 * Hook to use the performance context
 */
export function usePerformance() {
  return useContext(PerformanceContext);
}

export default PerformanceProvider;
