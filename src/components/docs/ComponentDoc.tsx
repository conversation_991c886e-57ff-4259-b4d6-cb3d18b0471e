'use client';

/**
 * ComponentDoc
 * 
 * This component provides documentation for UI components.
 */

import React from 'react';
import { cn } from '@/lib/utils';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Code, Info, Lightbulb, Palette, Settings } from 'lucide-react';

interface ComponentDocProps {
  /** Component name */
  name: string;
  /** Component description */
  description: string;
  /** Component example */
  example: React.ReactNode;
  /** Component code */
  code: string;
  /** Component props */
  props?: Array<{
    name: string;
    type: string;
    default?: string;
    description: string;
    required?: boolean;
  }>;
  /** Component usage examples */
  usage?: Array<{
    title: string;
    description?: string;
    code: string;
    example: React.ReactNode;
  }>;
  /** Component accessibility notes */
  accessibility?: string;
  /** Component design notes */
  design?: string;
  /** Additional class names */
  className?: string;
}

/**
 * A component for documenting UI components
 */
export function ComponentDoc({
  name,
  description,
  example,
  code,
  props,
  usage,
  accessibility,
  design,
  className,
}: ComponentDocProps) {
  return (
    <Card className={cn('overflow-hidden', className)}>
      <CardHeader>
        <CardTitle>{name}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="example" className="w-full">
          <TabsList className="w-full border-b border-border/20 rounded-none justify-start px-4">
            <TabsTrigger value="example" className="data-[state=active]:bg-muted/50">
              <Palette className="h-4 w-4 mr-2" />
              Example
            </TabsTrigger>
            <TabsTrigger value="code" className="data-[state=active]:bg-muted/50">
              <Code className="h-4 w-4 mr-2" />
              Code
            </TabsTrigger>
            {props && props.length > 0 && (
              <TabsTrigger value="props" className="data-[state=active]:bg-muted/50">
                <Settings className="h-4 w-4 mr-2" />
                Props
              </TabsTrigger>
            )}
            {(accessibility || design) && (
              <TabsTrigger value="notes" className="data-[state=active]:bg-muted/50">
                <Info className="h-4 w-4 mr-2" />
                Notes
              </TabsTrigger>
            )}
            {usage && usage.length > 0 && (
              <TabsTrigger value="usage" className="data-[state=active]:bg-muted/50">
                <Lightbulb className="h-4 w-4 mr-2" />
                Usage
              </TabsTrigger>
            )}
          </TabsList>
          
          {/* Example tab */}
          <TabsContent value="example" className="p-4 border-b border-border/20">
            <div className="flex items-center justify-center p-4 border border-border/20 rounded-lg bg-muted/20">
              {example}
            </div>
          </TabsContent>
          
          {/* Code tab */}
          <TabsContent value="code" className="p-4 border-b border-border/20">
            <pre className="p-4 rounded-lg bg-muted/20 overflow-auto">
              <code className="text-sm">{code}</code>
            </pre>
          </TabsContent>
          
          {/* Props tab */}
          {props && props.length > 0 && (
            <TabsContent value="props" className="p-4 border-b border-border/20">
              <div className="overflow-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b border-border/20">
                      <th className="py-2 px-4 text-left">Name</th>
                      <th className="py-2 px-4 text-left">Type</th>
                      <th className="py-2 px-4 text-left">Default</th>
                      <th className="py-2 px-4 text-left">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {props.map((prop) => (
                      <tr key={prop.name} className="border-b border-border/10">
                        <td className="py-2 px-4">
                          <code className="text-sm font-semibold">
                            {prop.name}
                            {prop.required && <span className="text-destructive">*</span>}
                          </code>
                        </td>
                        <td className="py-2 px-4">
                          <code className="text-xs bg-muted/30 px-1 py-0.5 rounded">
                            {prop.type}
                          </code>
                        </td>
                        <td className="py-2 px-4">
                          {prop.default ? (
                            <code className="text-xs">{prop.default}</code>
                          ) : (
                            <span className="text-muted-foreground text-xs">-</span>
                          )}
                        </td>
                        <td className="py-2 px-4 text-sm">{prop.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </TabsContent>
          )}
          
          {/* Notes tab */}
          {(accessibility || design) && (
            <TabsContent value="notes" className="p-4 border-b border-border/20">
              {accessibility && (
                <div className="mb-4">
                  <h3 className="text-base font-semibold mb-2">Accessibility</h3>
                  <div className="text-sm text-muted-foreground">{accessibility}</div>
                </div>
              )}
              {design && (
                <div>
                  <h3 className="text-base font-semibold mb-2">Design</h3>
                  <div className="text-sm text-muted-foreground">{design}</div>
                </div>
              )}
            </TabsContent>
          )}
          
          {/* Usage tab */}
          {usage && usage.length > 0 && (
            <TabsContent value="usage" className="border-b border-border/20">
              <div className="divide-y divide-border/20">
                {usage.map((example, index) => (
                  <div key={index} className="p-4">
                    <h3 className="text-base font-semibold mb-2">{example.title}</h3>
                    {example.description && (
                      <p className="text-sm text-muted-foreground mb-4">{example.description}</p>
                    )}
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-1 p-4 border border-border/20 rounded-lg bg-muted/20">
                        {example.example}
                      </div>
                      <div className="flex-1">
                        <pre className="p-4 rounded-lg bg-muted/20 overflow-auto h-full">
                          <code className="text-sm">{example.code}</code>
                        </pre>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  );
}

export default ComponentDoc;
