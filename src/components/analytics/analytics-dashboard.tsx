'use client';

/**
 * Analytics Dashboard Component
 * 
 * This component displays analytics data for the application.
 */

import React, { useState, useEffect } from 'react';
import { analytics, AnalyticsEvent } from '@/lib/analytics-utils';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>, 
  <PERSON>hart, 
  Activity, 
  Clock, 
  Eye, 
  MousePointer, 
  AlertTriangle, 
  Zap,
  RefreshCw,
  Download,
  Trash,
  Search,
  Filter
} from 'lucide-react';

/**
 * Analytics dashboard props
 */
interface AnalyticsDashboardProps {
  /** Whether to auto refresh */
  autoRefresh?: boolean;
  /** Auto refresh interval in milliseconds */
  refreshInterval?: number;
  /** Whether to show controls */
  showControls?: boolean;
  /** Whether to show filters */
  showFilters?: boolean;
  /** Whether to show export */
  showExport?: boolean;
  /** Whether to show clear */
  showClear?: boolean;
  /** Whether to show search */
  showSearch?: boolean;
  /** Additional class names */
  className?: string;
}

/**
 * Analytics dashboard component
 */
export function AnalyticsDashboard({
  autoRefresh = true,
  refreshInterval = 5000,
  showControls = true,
  showFilters = true,
  showExport = true,
  showClear = true,
  showSearch = true,
  className,
}: AnalyticsDashboardProps) {
  const [events, setEvents] = useState<AnalyticsEvent[]>([]);
  const [filteredEvents, setFilteredEvents] = useState<AnalyticsEvent[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [eventTypeFilter, setEventTypeFilter] = useState<string>('all');
  const [timeRangeFilter, setTimeRangeFilter] = useState<string>('all');
  const [isRefreshing, setIsRefreshing] = useState(false);
  
  // Load events
  const loadEvents = () => {
    setIsRefreshing(true);
    const allEvents = analytics.getEvents();
    setEvents(allEvents);
    setIsRefreshing(false);
  };
  
  // Initial load
  useEffect(() => {
    loadEvents();
  }, []);
  
  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(() => {
      loadEvents();
    }, refreshInterval);
    
    return () => {
      clearInterval(interval);
    };
  }, [autoRefresh, refreshInterval]);
  
  // Apply filters
  useEffect(() => {
    let filtered = [...events];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(event => 
        event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        event.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (event.category && event.category.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (event.label && event.label.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (event.data && JSON.stringify(event.data).toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }
    
    // Apply event type filter
    if (eventTypeFilter !== 'all') {
      filtered = filtered.filter(event => event.type === eventTypeFilter);
    }
    
    // Apply time range filter
    if (timeRangeFilter !== 'all') {
      const now = Date.now();
      let timeRange = 0;
      
      switch (timeRangeFilter) {
        case '15m':
          timeRange = 15 * 60 * 1000;
          break;
        case '1h':
          timeRange = 60 * 60 * 1000;
          break;
        case '24h':
          timeRange = 24 * 60 * 60 * 1000;
          break;
        case '7d':
          timeRange = 7 * 24 * 60 * 60 * 1000;
          break;
        case '30d':
          timeRange = 30 * 24 * 60 * 60 * 1000;
          break;
      }
      
      filtered = filtered.filter(event => now - event.timestamp <= timeRange);
    }
    
    // Sort by timestamp (newest first)
    filtered.sort((a, b) => b.timestamp - a.timestamp);
    
    setFilteredEvents(filtered);
  }, [events, searchTerm, eventTypeFilter, timeRangeFilter]);
  
  // Handle refresh
  const handleRefresh = () => {
    loadEvents();
  };
  
  // Handle clear
  const handleClear = () => {
    if (window.confirm('Are you sure you want to clear all analytics data?')) {
      localStorage.removeItem('analytics_events');
      localStorage.removeItem('analytics_page_views');
      localStorage.removeItem('analytics_errors');
      localStorage.removeItem('analytics_performance');
      loadEvents();
    }
  };
  
  // Handle export
  const handleExport = () => {
    const data = {
      events,
      pageViews: JSON.parse(localStorage.getItem('analytics_page_views') || '[]'),
      errors: JSON.parse(localStorage.getItem('analytics_errors') || '[]'),
      performance: JSON.parse(localStorage.getItem('analytics_performance') || '{}'),
      exportDate: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-export-${new Date().toISOString()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  // Format date
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString();
  };
  
  // Get event type icon
  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'page_view':
        return <Eye className="h-4 w-4" />;
      case 'user_interaction':
        return <MousePointer className="h-4 w-4" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4" />;
      case 'performance':
        return <Zap className="h-4 w-4" />;
      case 'navigation':
        return <Activity className="h-4 w-4" />;
      case 'resource':
        return <Clock className="h-4 w-4" />;
      case 'component_render':
        return <BarChart className="h-4 w-4" />;
      case 'custom':
        return <PieChart className="h-4 w-4" />;
      default:
        return <LineChart className="h-4 w-4" />;
    }
  };
  
  // Get event counts by type
  const getEventCountsByType = () => {
    const counts: Record<string, number> = {};
    
    events.forEach(event => {
      counts[event.type] = (counts[event.type] || 0) + 1;
    });
    
    return counts;
  };
  
  // Get page view counts
  const getPageViewCounts = () => {
    const counts: Record<string, number> = {};
    
    events.filter(event => event.type === 'page_view').forEach(event => {
      const path = event.data?.path || 'unknown';
      counts[path] = (counts[path] || 0) + 1;
    });
    
    return counts;
  };
  
  // Get error counts
  const getErrorCounts = () => {
    return events.filter(event => event.type === 'error').length;
  };
  
  // Get performance metrics
  const getPerformanceMetrics = () => {
    const metrics: Record<string, number> = {};
    
    events.filter(event => event.type === 'performance').forEach(event => {
      metrics[event.name] = event.data?.value || 0;
    });
    
    return metrics;
  };
  
  return (
    <div className="space-y-4">
      {/* Controls */}
      {showControls && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle>Analytics Dashboard</CardTitle>
            <CardDescription>
              View and analyze application usage and performance data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              
              {showExport && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleExport}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              )}
              
              {showClear && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleClear}
                >
                  <Trash className="h-4 w-4 mr-2" />
                  Clear
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-base">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {showSearch && (
                <div className="space-y-2">
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="Search events..."
                      className="pl-8"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
              )}
              
              <div className="space-y-2">
                <Label htmlFor="event-type">Event Type</Label>
                <div className="relative">
                  <Filter className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <select
                    id="event-type"
                    className="w-full pl-8 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={eventTypeFilter}
                    onChange={(e) => setEventTypeFilter(e.target.value)}
                  >
                    <option value="all">All Types</option>
                    <option value="page_view">Page Views</option>
                    <option value="user_interaction">User Interactions</option>
                    <option value="error">Errors</option>
                    <option value="performance">Performance</option>
                    <option value="navigation">Navigation</option>
                    <option value="resource">Resources</option>
                    <option value="component_render">Component Renders</option>
                    <option value="custom">Custom Events</option>
                  </select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="time-range">Time Range</Label>
                <div className="relative">
                  <Clock className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <select
                    id="time-range"
                    className="w-full pl-8 h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
                    value={timeRangeFilter}
                    onChange={(e) => setTimeRangeFilter(e.target.value)}
                  >
                    <option value="all">All Time</option>
                    <option value="15m">Last 15 Minutes</option>
                    <option value="1h">Last Hour</option>
                    <option value="24h">Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                  </select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
      
      {/* Dashboard */}
      <Tabs defaultValue="overview">
        <TabsList className="w-full border-b border-border/20 rounded-none justify-start px-4 mb-6">
          <TabsTrigger value="overview" className="data-[state=active]:bg-muted/50">
            Overview
          </TabsTrigger>
          <TabsTrigger value="events" className="data-[state=active]:bg-muted/50">
            Events
          </TabsTrigger>
          <TabsTrigger value="performance" className="data-[state=active]:bg-muted/50">
            Performance
          </TabsTrigger>
          <TabsTrigger value="errors" className="data-[state=active]:bg-muted/50">
            Errors
          </TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Total Events</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{events.length}</div>
                <p className="text-sm text-muted-foreground">
                  {filteredEvents.length} filtered events
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Page Views</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {events.filter(event => event.type === 'page_view').length}
                </div>
                <p className="text-sm text-muted-foreground">
                  {Object.keys(getPageViewCounts()).length} unique pages
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Errors</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{getErrorCounts()}</div>
                <p className="text-sm text-muted-foreground">
                  {events.filter(event => event.type === 'error' && event.data?.type === 'unhandledrejection').length} unhandled rejections
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        {/* Events Tab */}
        <TabsContent value="events">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Event Log</CardTitle>
              <CardDescription>
                {filteredEvents.length} events found
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-auto">
                {filteredEvents.length > 0 ? (
                  filteredEvents.map((event, index) => (
                    <div key={index} className="p-3 border border-border/20 rounded-lg">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="p-1 rounded-full bg-muted/30">
                          {getEventTypeIcon(event.type)}
                        </div>
                        <div className="font-medium">{event.name}</div>
                        <div className="text-xs text-muted-foreground ml-auto">
                          {formatDate(event.timestamp)}
                        </div>
                      </div>
                      <div className="text-xs text-muted-foreground">
                        Type: {event.type}
                        {event.category && ` • Category: ${event.category}`}
                        {event.label && ` • Label: ${event.label}`}
                        {event.duration !== undefined && ` • Duration: ${event.duration.toFixed(2)}ms`}
                      </div>
                      {event.data && (
                        <div className="mt-2 text-xs">
                          <details>
                            <summary className="cursor-pointer">Data</summary>
                            <pre className="mt-2 p-2 bg-muted/20 rounded-lg overflow-auto">
                              {JSON.stringify(event.data, null, 2)}
                            </pre>
                          </details>
                        </div>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No events found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Performance Tab */}
        <TabsContent value="performance">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Performance Metrics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(getPerformanceMetrics()).length > 0 ? (
                  Object.entries(getPerformanceMetrics()).map(([metric, value]) => (
                    <div key={metric} className="space-y-1">
                      <div className="flex justify-between">
                        <span className="font-medium">{metric}</span>
                        <span>{value}</span>
                      </div>
                      <div className="w-full bg-muted/30 rounded-full h-2">
                        <div
                          className="bg-primary rounded-full h-2"
                          style={{ width: `${Math.min(100, (value / 1000) * 100)}%` }}
                        />
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No performance metrics found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Errors Tab */}
        <TabsContent value="errors">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle>Error Log</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 max-h-96 overflow-auto">
                {events.filter(event => event.type === 'error').length > 0 ? (
                  events
                    .filter(event => event.type === 'error')
                    .map((event, index) => (
                      <div key={index} className="p-3 border border-border/20 rounded-lg">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="p-1 rounded-full bg-red-500/10">
                            <AlertTriangle className="h-4 w-4 text-red-500" />
                          </div>
                          <div className="font-medium">{event.data?.message || 'Error'}</div>
                          <div className="text-xs text-muted-foreground ml-auto">
                            {formatDate(event.timestamp)}
                          </div>
                        </div>
                        {event.data?.stack && (
                          <div className="mt-2 text-xs">
                            <details>
                              <summary className="cursor-pointer">Stack Trace</summary>
                              <pre className="mt-2 p-2 bg-muted/20 rounded-lg overflow-auto">
                                {event.data.stack}
                              </pre>
                            </details>
                          </div>
                        )}
                      </div>
                    ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    No errors found
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default AnalyticsDashboard;
