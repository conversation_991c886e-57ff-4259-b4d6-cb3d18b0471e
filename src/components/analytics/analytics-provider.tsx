'use client';

/**
 * Analytics Provider Component
 * 
 * This component provides analytics tracking for the application.
 */

import React, { useEffect, useState } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';
import { analytics, AnalyticsProvider as AnalyticsProviderType } from '@/lib/analytics-utils';
import { usePerformance } from '@/components/performance/performance-provider';

/**
 * Console analytics provider
 */
export const consoleAnalyticsProvider: AnalyticsProviderType = {
  name: 'console',
  trackEvent: (event) => {
    console.log('[Analytics]', event);
  },
  trackPageView: (path, title, referrer) => {
    console.log('[Analytics] Page View:', { path, title, referrer });
  },
  trackError: (error, context) => {
    console.error('[Analytics] Error:', error, context);
  },
  trackPerformance: (metric, value, unit) => {
    console.log('[Analytics] Performance:', { metric, value, unit });
  },
  initialize: () => {
    console.log('[Analytics] Console provider initialized');
  },
};

/**
 * Local storage analytics provider
 */
export const localStorageAnalyticsProvider: AnalyticsProviderType = {
  name: 'localStorage',
  trackEvent: (event) => {
    try {
      const events = JSON.parse(localStorage.getItem('analytics_events') || '[]');
      events.push(event);
      // Keep only the last 100 events
      const limitedEvents = events.slice(-100);
      localStorage.setItem('analytics_events', JSON.stringify(limitedEvents));
    } catch (error) {
      console.error('[Analytics] Error storing event in localStorage:', error);
    }
  },
  trackPageView: (path, title, referrer) => {
    try {
      const pageViews = JSON.parse(localStorage.getItem('analytics_page_views') || '[]');
      pageViews.push({ path, title, referrer, timestamp: Date.now() });
      // Keep only the last 50 page views
      const limitedPageViews = pageViews.slice(-50);
      localStorage.setItem('analytics_page_views', JSON.stringify(limitedPageViews));
    } catch (error) {
      console.error('[Analytics] Error storing page view in localStorage:', error);
    }
  },
  trackError: (error, context) => {
    try {
      const errors = JSON.parse(localStorage.getItem('analytics_errors') || '[]');
      errors.push({
        message: error.message,
        stack: error.stack,
        context,
        timestamp: Date.now(),
      });
      // Keep only the last 20 errors
      const limitedErrors = errors.slice(-20);
      localStorage.setItem('analytics_errors', JSON.stringify(limitedErrors));
    } catch (error) {
      console.error('[Analytics] Error storing error in localStorage:', error);
    }
  },
  trackPerformance: (metric, value, unit) => {
    try {
      const performance = JSON.parse(localStorage.getItem('analytics_performance') || '{}');
      performance[metric] = { value, unit, timestamp: Date.now() };
      localStorage.setItem('analytics_performance', JSON.stringify(performance));
    } catch (error) {
      console.error('[Analytics] Error storing performance in localStorage:', error);
    }
  },
  initialize: () => {
    console.log('[Analytics] LocalStorage provider initialized');
  },
};

/**
 * Analytics provider props
 */
interface AnalyticsProviderProps {
  /** Children components */
  children: React.ReactNode;
  /** Whether to enable analytics */
  enabled?: boolean;
  /** Whether to use debug mode */
  debug?: boolean;
  /** Whether to track page views */
  trackPageViews?: boolean;
  /** Whether to track errors */
  trackErrors?: boolean;
  /** Whether to track performance */
  trackPerformance?: boolean;
  /** Whether to track user interactions */
  trackUserInteractions?: boolean;
  /** Whether to track resource loading */
  trackResources?: boolean;
  /** Whether to track navigation */
  trackNavigation?: boolean;
  /** Whether to track component renders */
  trackComponentRenders?: boolean;
  /** Whether to track custom events */
  trackCustomEvents?: boolean;
  /** Sampling rate (0-1) */
  samplingRate?: number;
  /** Analytics providers */
  providers?: AnalyticsProviderType[];
}

/**
 * Analytics provider component
 */
export function AnalyticsProvider({
  children,
  enabled = process.env.NODE_ENV === 'production',
  debug = process.env.NODE_ENV === 'development',
  trackPageViews = true,
  trackErrors = true,
  trackPerformance = true,
  trackUserInteractions = true,
  trackResources = true,
  trackNavigation = true,
  trackComponentRenders = false,
  trackCustomEvents = true,
  samplingRate = 1,
  providers = [],
}: AnalyticsProviderProps) {
  const [isInitialized, setIsInitialized] = useState(false);
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const performance = usePerformance();
  
  // Initialize analytics
  useEffect(() => {
    if (isInitialized) return;
    
    // Add default providers in development mode
    const allProviders = [...providers];
    if (process.env.NODE_ENV === 'development') {
      if (debug) {
        allProviders.push(consoleAnalyticsProvider);
      }
      allProviders.push(localStorageAnalyticsProvider);
    }
    
    // Update analytics configuration
    analytics.updateConfig({
      enabled,
      debug,
      trackPageViews,
      trackErrors,
      trackPerformance,
      trackUserInteractions,
      trackResources,
      trackNavigation,
      trackComponentRenders,
      trackCustomEvents,
      samplingRate,
      providers: allProviders,
    });
    
    // Initialize analytics
    analytics.initialize();
    setIsInitialized(true);
  }, [
    isInitialized,
    enabled,
    debug,
    trackPageViews,
    trackErrors,
    trackPerformance,
    trackUserInteractions,
    trackResources,
    trackNavigation,
    trackComponentRenders,
    trackCustomEvents,
    samplingRate,
    providers,
  ]);
  
  // Track page views
  useEffect(() => {
    if (!isInitialized || !trackPageViews) return;
    
    // Track page view
    analytics.trackPageView(
      pathname,
      document.title,
      document.referrer
    );
    
    // Track performance
    if (trackPerformance) {
      // Track device performance
      analytics.trackCustomEvent('device_performance', {
        isLowEndDevice: performance.isLowEndDevice,
        isLowMemory: performance.isLowMemory,
        isDataSaver: performance.isDataSaver,
        isReducedMotion: performance.hasReducedMotion,
        connectionType: performance.connectionType,
        effectiveConnectionType: performance.effectiveConnectionType,
        isMobile: performance.isMobile,
        isTablet: performance.isTablet,
        isDesktop: performance.isDesktop,
        isPortrait: performance.isPortrait,
        isLandscape: performance.isLandscape,
      });
    }
  }, [isInitialized, trackPageViews, trackPerformance, pathname, searchParams, performance]);
  
  // Clean up
  useEffect(() => {
    return () => {
      if (isInitialized) {
        analytics.cleanup();
      }
    };
  }, [isInitialized]);
  
  return <>{children}</>;
}

/**
 * Hook to track component render time
 */
export function useTrackComponentRender(componentName: string, props?: Record<string, any>) {
  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      analytics.trackComponentRender(componentName, renderTime, props);
    };
  }, [componentName, props]);
}

/**
 * Higher-order component to track component render time
 */
export function withAnalytics<P extends object>(
  Component: React.ComponentType<P>,
  componentName: string
) {
  const displayName = componentName || Component.displayName || Component.name || 'Component';
  
  const WrappedComponent = (props: P) => {
    useTrackComponentRender(displayName, props as Record<string, any>);
    
    return <Component {...props} />;
  };
  
  WrappedComponent.displayName = `withAnalytics(${displayName})`;
  
  return WrappedComponent;
}

export default AnalyticsProvider;
