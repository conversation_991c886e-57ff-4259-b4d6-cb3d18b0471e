'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  /** Container content */
  children: React.ReactNode;
  /** Optional className for styling */
  className?: string;
  /** Maximum width of the container */
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none';
  /** Padding for the container */
  padding?: 'none' | 'sm' | 'md' | 'lg';
  /** Whether to center the container */
  centered?: boolean;
  /** Whether to add a border to the container */
  bordered?: boolean;
  /** Whether to add a shadow to the container */
  shadowed?: boolean;
  /** Whether to add a rounded corner to the container */
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  /** Whether to add a background color to the container */
  background?: boolean;
  /** Whether to use a glass effect */
  glass?: boolean;
}

/**
 * A responsive container component that follows iOS 19 design principles
 */
export function ResponsiveContainer({
  children,
  className,
  maxWidth = 'xl',
  padding = 'md',
  centered = true,
  bordered = false,
  shadowed = false,
  rounded = 'lg',
  background = false,
  glass = false,
}: ResponsiveContainerProps) {
  // Map maxWidth to Tailwind classes
  const maxWidthClasses = {
    sm: 'max-w-screen-sm',
    md: 'max-w-screen-md',
    lg: 'max-w-screen-lg',
    xl: 'max-w-screen-xl',
    '2xl': 'max-w-screen-2xl',
    full: 'max-w-full',
    none: '',
  };

  // Map padding to Tailwind classes with responsive values
  const paddingClasses = {
    none: 'p-0',
    sm: 'px-3 py-2 sm:px-4 sm:py-3',
    md: 'px-4 py-3 sm:px-6 sm:py-4 lg:px-8 lg:py-6',
    lg: 'px-5 py-4 sm:px-8 sm:py-6 lg:px-12 lg:py-8',
  };

  // Map rounded to Tailwind classes
  const roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    xl: 'rounded-xl',
    '2xl': 'rounded-2xl',
    full: 'rounded-full',
  };

  return (
    <div
      className={cn(
        // Base styles
        'w-full',
        
        // Max width
        maxWidthClasses[maxWidth],
        
        // Padding
        paddingClasses[padding],
        
        // Centered
        centered && 'mx-auto',
        
        // Border
        bordered && 'border border-border/40',
        
        // Shadow
        shadowed && 'shadow-sm',
        
        // Rounded
        roundedClasses[rounded],
        
        // Background
        background && 'bg-card',
        
        // Glass effect
        glass && 'bg-card/80 backdrop-blur-sm',
        
        // Custom className
        className
      )}
    >
      {children}
    </div>
  );
}

export default ResponsiveContainer;
