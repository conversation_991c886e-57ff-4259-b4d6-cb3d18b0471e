import * as React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

/**
 * Card component variants following iOS 19 design principles
 */
const cardVariants = cva(
  'bg-card text-card-foreground rounded-lg overflow-hidden transition-all duration-200',
  {
    variants: {
      variant: {
        default: 'border border-border/40 shadow-sm bg-card',
        elevated: 'border border-border/30 shadow-md bg-card',
        outline: 'border border-border bg-transparent',
        ghost: 'bg-transparent border-none shadow-none',
        glass: 'bg-card/80 backdrop-blur-sm border border-border/30',
        primary: 'border-l-4 border-primary bg-card',
        secondary: 'border-l-4 border-secondary bg-card',
        accent: 'border-l-4 border-accent bg-card',
        info: 'border-l-4 border-info bg-card',
        success: 'border-l-4 border-success bg-card',
        warning: 'border-l-4 border-warning bg-card',
        destructive: 'border-l-4 border-destructive bg-card',
      },
      size: {
        default: 'p-4 sm:p-5 space-y-4',
        sm: 'p-3 sm:p-4 space-y-3',
        lg: 'p-5 sm:p-6 lg:p-8 space-y-5',
        compact: 'p-2 sm:p-3 space-y-2',
        none: 'p-0',
      },
      hover: {
        default: 'transition-all duration-200',
        subtle: 'transition-all duration-200 hover:shadow-md hover:border-primary/20',
        glow: 'transition-all duration-200 hover:shadow-lg hover:border-primary/50',
        scale: 'transition-all duration-200 hover:scale-[1.01] hover:shadow-md',
        lift: 'transition-all duration-200 hover:-translate-y-1 hover:shadow-md',
        pulse: 'transition-all duration-200 hover:shadow-md hover:border-primary/20 hover:animate-pulse',
        none: '',
      },
      rounded: {
        none: 'rounded-none',
        sm: 'rounded-sm',
        md: 'rounded-md',
        lg: 'rounded-lg',
        xl: 'rounded-xl',
        '2xl': 'rounded-2xl',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      hover: 'none',
      rounded: 'lg',
    },
  }
);

export interface CardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof cardVariants> {
  /** Whether the card is interactive */
  interactive?: boolean;
  /** Whether the card should have a glass effect */
  glass?: boolean;
  /** Whether the card should have a border */
  bordered?: boolean;
  /** Whether the card should have a shadow */
  shadowed?: boolean;
}

/**
 * Card component that follows iOS 19 design principles
 */
const Card = React.forwardRef<HTMLDivElement, CardProps>(
  ({
    className,
    variant,
    size,
    hover,
    rounded,
    interactive = false,
    glass = false,
    bordered = true,
    shadowed = false,
    ...props
  }, ref) => (
    <div
      ref={ref}
      data-slot="card"
      className={cn(
        cardVariants({
          variant: glass ? 'glass' : variant,
          size,
          hover,
          rounded
        }),
        interactive && 'cursor-pointer active:scale-[0.98] transition-transform duration-200',
        !bordered && 'border-0',
        shadowed && !variant?.includes('elevated') && 'shadow-md',
        className
      )}
      {...props}
    />
  )
);
Card.displayName = 'Card';

/**
 * Card header component that follows iOS 19 design principles
 */
const CardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    /** Whether the card header should have a border */
    bordered?: boolean;
    /** Whether the card header should be sticky */
    sticky?: boolean;
  }
>(({ className, bordered = false, sticky = false, ...props }, ref) => (
  <div
    ref={ref}
    data-slot="card-header"
    className={cn(
      'flex flex-col space-y-1.5 p-4 sm:p-5',
      bordered && 'border-b border-border/30 pb-3 mb-1',
      sticky && 'sticky top-0 z-10 bg-card/95 backdrop-blur-sm',
      className
    )}
    {...props}
  />
));
CardHeader.displayName = 'CardHeader';

/**
 * Card title component that follows iOS 19 design principles
 */
const CardTitle = React.forwardRef<
  HTMLHeadingElement,
  React.HTMLAttributes<HTMLHeadingElement> & { as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' }
>(({ className, as: Comp = 'h3', ...props }, ref) => (
  <Comp
    ref={ref}
    data-slot="card-title"
    className={cn('text-xl font-semibold leading-tight tracking-tight', className)}
    {...props}
  />
));
CardTitle.displayName = 'CardTitle';

/**
 * Card description component that follows iOS 19 design principles
 */
const CardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    data-slot="card-description"
    className={cn('text-sm text-muted-foreground', className)}
    {...props}
  />
));
CardDescription.displayName = 'CardDescription';

/**
 * Card content component that follows iOS 19 design principles
 */
const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    /** Whether to remove padding from the content */
    noPadding?: boolean;
  }
>(({ className, noPadding = false, ...props }, ref) => (
  <div
    ref={ref}
    data-slot="card-content"
    className={cn(
      noPadding ? '' : 'px-4 sm:px-5 py-2 sm:py-3',
      'overflow-auto',
      className
    )}
    {...props}
  />
));
CardContent.displayName = 'CardContent';

/**
 * Card footer component that follows iOS 19 design principles
 */
const CardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    /** Whether the card footer should have a border */
    bordered?: boolean;
    /** Whether the card footer should be sticky */
    sticky?: boolean;
  }
>(({ className, bordered = false, sticky = false, ...props }, ref) => (
  <div
    ref={ref}
    data-slot="card-footer"
    className={cn(
      'flex items-center justify-between p-4 sm:p-5',
      bordered && 'border-t border-border/30 pt-3 mt-1',
      sticky && 'sticky bottom-0 z-10 bg-card/95 backdrop-blur-sm',
      className
    )}
    {...props}
  />
));
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, cardVariants };
