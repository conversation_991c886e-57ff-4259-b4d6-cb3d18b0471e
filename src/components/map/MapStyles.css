/* Custom styles for Leaflet map */

/* Ensure map container doesn't overlap with header */
.leaflet-container {
  z-index: 0 !important;
}

.leaflet-control-container {
  z-index: 10 !important;
}

.leaflet-pane {
  z-index: 5 !important;
}

.leaflet-top,
.leaflet-bottom {
  z-index: 10 !important;
}

/* Marker styles */
.custom-marker-icon {
  background: transparent;
  border: none;
}

.marker-normal {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.9);
  color: hsl(var(--foreground));
  font-weight: 600;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 50%;
  font-size: 12px;
  z-index: 15 !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.marker-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
  font-weight: 700;
  border-radius: 50%;
  font-size: 14px;
  z-index: 20 !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
  border: 2px solid white;
}

/* Popup styles - iOS 19 style */
.weather-popup {
  text-align: center;
  padding: 12px;
  min-width: 140px;
}

.weather-temp {
  font-weight: 700;
  font-size: 18px;
  margin-bottom: 6px;
}

.weather-wind {
  font-size: 13px;
  font-weight: 500;
  color: hsl(var(--muted-foreground));
}

.leaflet-popup-content-wrapper {
  padding: 0;
  border: 1px solid rgba(0, 0, 0, 0.05);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 25 !important;
  background-color: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
}

.leaflet-popup-content {
  margin: 12px;
}

.leaflet-popup {
  z-index: 25 !important;
}

.leaflet-popup-tip {
  background-color: rgba(255, 255, 255, 0.9);
  -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.leaflet-container {
  font: inherit;
}

/* Dark mode support - iOS 19 style */
.dark .leaflet-tile {
  filter: brightness(0.7) invert(1) contrast(1.5) hue-rotate(200deg) saturate(0.5) brightness(0.8);
}

.dark .leaflet-container {
  background: #1a1a1a;
}

.dark .leaflet-control-attribution {
  background: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.7);
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border-radius: 4px;
}

.dark .leaflet-control-attribution a {
  color: rgba(255, 255, 255, 0.8);
}

/* Map control buttons - iOS 19 style */
.map-control-button {
  background-color: rgba(255, 255, 255, 0.8);
  color: hsl(var(--foreground));
  border: 1px solid rgba(0, 0, 0, 0.05);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
  z-index: 500 !important;
  border-radius: 50%;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.map-control-button:focus {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
}

.map-control-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Weather info panel */
.leaflet-container ~ div > .absolute.bottom-4 {
  position: relative;
  z-index: 400 !important;
}

/* Responsive styles - iOS 19 style */
@media (max-width: 768px) {
  .marker-normal {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }

  .marker-selected {
    width: 40px;
    height: 40px;
    font-size: 12px;
  }

  .map-control-button {
    width: 38px;
    height: 38px;
  }

  .weather-popup {
    min-width: 120px;
    padding: 10px;
  }

  .weather-temp {
    font-size: 16px;
  }

  .weather-wind {
    font-size: 12px;
  }
}
