'use client';

/**
 * MapWrapper Component
 *
 * This component provides a wrapper for the map component that handles dynamic loading
 * following iOS 19 design principles.
 */

import React from 'react';
import { GPXData, ForecastPoint, WeatherData } from '@/types';
import { cn } from '@/lib/utils';
import ModernMap from './ModernMap';

interface MapWrapperProps {
  /** GPX data containing route information */
  gpxData?: GPXData;
  /** Array of forecast points along the route */
  forecastPoints: ForecastPoint[];
  /** Weather data for each forecast point */
  weatherData?: (WeatherData | null)[];
  /** Callback for when a marker is clicked */
  onMarkerClick?: (index: number) => void;
  /** Index of the currently selected marker */
  selectedMarker?: number | null;
  /** Additional class names */
  className?: string;
  /** Whether to show a glass effect */
  glass?: boolean;
  /** Whether to show a border */
  bordered?: boolean;
  /** Whether to show a shadow */
  shadowed?: boolean;
  /** Whether to show rounded corners */
  rounded?: boolean;
}

/**
 * A wrapper component for the map that handles dynamic loading
 * following iOS 19 design principles
 */
export default function MapWrapper({
  gpxData,
  forecastPoints,
  weatherData = [],
  onMarkerClick = () => {},
  selectedMarker = null,
  className,
  glass = true,
  bordered = true,
  shadowed = true,
  rounded = true,
}: MapWrapperProps): JSX.Element {
  return (
    <ModernMap
      gpxData={gpxData || null}
      forecastPoints={forecastPoints}
      weatherData={weatherData}
      onMarkerClick={onMarkerClick}
      selectedMarker={selectedMarker}
      className={className}
      glass={glass}
      bordered={bordered}
      shadowed={shadowed}
      rounded={rounded}
      showControls={true}
      showFullscreenButton={true}
    />
  );
}
