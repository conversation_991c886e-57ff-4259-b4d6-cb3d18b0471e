'use client';

/**
 * Charts Component
 *
 * This component provides a tabbed interface for displaying different weather charts
 * following iOS 19 design principles.
 */

import React from 'react';
import { GPXData, ForecastPoint, WeatherData } from '@/types';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/card';

// Import chart components
import TemperatureChart from './TemperatureChart';
import PrecipitationChart from './PrecipitationChart';
import WindChart from './WindChart';
import Humi<PERSON>y<PERSON>hart from './HumidityChart';
import Pressure<PERSON>hart from './PressureChart';
import Elevation<PERSON>hart from './ElevationChart';
import UVIndexChart from './UVIndexChart';

// Import modern chart components
import { ModernChartTabs, ModernChartTabContent } from './ModernChartTabs';

interface ChartsProps {
  /** GPX data containing route points */
  gpxData: GPXData | null;
  /** Forecast points along the route */
  forecastPoints: ForecastPoint[];
  /** Weather data for each forecast point */
  weatherData: (WeatherData | null)[];
  /** Currently selected marker index */
  selectedMarker: number | null;
  /** Callback when a chart point is clicked */
  onChartClick?: (index: number) => void;
  /** Additional class names */
  className?: string;
}

/**
 * A component that displays various charts for weather and route data
 * following iOS 19 design principles
 */
export function Charts({
  gpxData,
  forecastPoints,
  weatherData,
  selectedMarker,
  onChartClick,
  className,
}: ChartsProps) {
  return (
    <Card
      className={cn('overflow-hidden w-full h-full', className)}
      variant="glass"
      rounded="xl"
    >
      <ModernChartTabs defaultTab="temperature" showIcons={true}>
        <ModernChartTabContent value="temperature">
          <TemperatureChart
            forecastPoints={forecastPoints}
            weatherData={weatherData}
            selectedMarker={selectedMarker}
            onChartClick={onChartClick}
            delay={0}
          />
        </ModernChartTabContent>

        <ModernChartTabContent value="precipitation">
          <PrecipitationChart
            forecastPoints={forecastPoints}
            weatherData={weatherData}
            selectedMarker={selectedMarker}
            onChartClick={onChartClick}
            delay={0.1}
          />
        </ModernChartTabContent>

        <ModernChartTabContent value="wind">
          <WindChart
            forecastPoints={forecastPoints}
            weatherData={weatherData}
            selectedMarker={selectedMarker}
            onChartClick={onChartClick}
            delay={0.2}
          />
        </ModernChartTabContent>

        <ModernChartTabContent value="humidity">
          <HumidityChart
            forecastPoints={forecastPoints}
            weatherData={weatherData}
            selectedMarker={selectedMarker}
            onChartClick={onChartClick}
            delay={0.3}
          />
        </ModernChartTabContent>

        <ModernChartTabContent value="pressure">
          <PressureChart
            forecastPoints={forecastPoints}
            weatherData={weatherData}
            selectedMarker={selectedMarker}
            onChartClick={onChartClick}
            delay={0.4}
          />
        </ModernChartTabContent>

        <ModernChartTabContent value="elevation">
          <ElevationChart
            gpxData={gpxData}
            forecastPoints={forecastPoints}
            selectedMarker={selectedMarker}
            onChartClick={onChartClick}
            delay={0.5}
          />
        </ModernChartTabContent>

        <ModernChartTabContent value="uv-index">
          <UVIndexChart
            forecastPoints={forecastPoints}
            weatherData={weatherData}
            selectedMarker={selectedMarker}
            onChartClick={onChartClick}
            delay={0.6}
          />
        </ModernChartTabContent>
      </ModernChartTabs>
    </Card>
  );
}
