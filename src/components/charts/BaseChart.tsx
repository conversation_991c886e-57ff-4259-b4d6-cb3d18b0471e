'use client';

/**
 * BaseChart Component
 *
 * This component provides a consistent card container for charts
 * following iOS 19 design principles.
 */

import React from 'react';
import { ForecastPoint, WeatherData } from '@/types';
import { cn } from '@/lib/utils';
import ModernChartCard from './ModernChartCard';
import '@/styles/chart-styles.css';

export interface BaseChartProps {
  /** Forecast points along the route */
  forecastPoints: ForecastPoint[];
  /** Weather data for each forecast point */
  weatherData?: (WeatherData | null)[];
  /** Currently selected marker index */
  selectedMarker: number | null;
  /** Callback when a chart point is clicked */
  onChartClick?: (index: number) => void;
  /** Additional class names */
  className?: string;
  /** Chart title */
  title: string;
  /** Unit label (e.g., °C, mm, etc.) */
  unitLabel?: string;
  /** Optional description or tooltip content */
  description?: string;
  /** Whether to show a loading state */
  loading?: boolean;
  /** Animation delay in seconds */
  delay?: number;
}

/**
 * Base chart component that provides common functionality for all charts
 * following iOS 19 design principles
 */
const BaseChart: React.FC<BaseChartProps & { children: React.ReactNode }> = ({
  title,
  unitLabel,
  description,
  children,
  className,
  loading = false,
  delay = 0,
}) => {
  return (
    <ModernChartCard
      title={title}
      unitLabel={unitLabel}
      description={description}
      className={cn("w-full", className)}
      loading={loading}
      glass={true}
      bordered={true}
      shadowed={true}
      rounded={true}
    >
      {children}
    </ModernChartCard>
  );
};

export default BaseChart;
