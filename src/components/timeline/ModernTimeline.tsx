'use client';

/**
 * ModernTimeline Component
 *
 * This component displays a horizontal timeline of weather data points.
 * It uses the TimelineItem component to render each individual point.
 *
 * Related components:
 * - TimelineItem: Individual timeline items
 * - ModernClientTimeline: Client-side wrapper for this component
 * - ModernTimelineWrapper: Error handling wrapper
 */

import React, { useRef, useEffect } from 'react';
import { ForecastPoint, WeatherData } from '@/types';
import { cn } from '@/lib/utils';
import TimelineItem from './TimelineItem';

interface ModernTimelineProps {
  /** Forecast points along the route */
  forecastPoints: ForecastPoint[];
  /** Weather data for each forecast point */
  weatherData: (WeatherData | null)[];
  /** Currently selected marker index */
  selectedMarker: number | null;
  /** Callback when a timeline item is clicked */
  onTimelineClick?: (index: number) => void;
  /** Optional className for styling */
  className?: string;
  /** Reference to the timeline container for scrolling */
  timelineRef?: React.RefObject<HTMLDivElement | null>;
}

/**
 * A modern timeline component that displays weather data for each point along a route
 * Following iOS 19 design principles
 */
export function ModernTimeline({
  forecastPoints,
  weatherData,
  selectedMarker,
  onTimelineClick,
  className,
  timelineRef,
}: ModernTimelineProps) {
  const localTimelineRef = useRef<HTMLDivElement>(null);
  const containerRef = timelineRef || localTimelineRef;

  // Scroll to selected marker when it changes
  useEffect(() => {
    if (selectedMarker !== null && containerRef.current) {
      const timelineItems = containerRef.current.querySelectorAll('.timeline-item');
      if (timelineItems[selectedMarker]) {
        const item = timelineItems[selectedMarker] as HTMLElement;
        const container = containerRef.current;

        // Calculate the scroll position to center the selected item
        const itemLeft = item.offsetLeft;
        const itemWidth = item.offsetWidth;
        const containerWidth = container.offsetWidth;
        const scrollLeft = itemLeft - containerWidth / 2 + itemWidth / 2;

        // Smooth scroll to the position
        container.scrollTo({
          left: scrollLeft,
          behavior: 'smooth',
        });
      }
    }
  }, [selectedMarker, containerRef]);

  // Handle timeline item click
  const handleItemClick = (index: number) => {
    if (onTimelineClick) {
      onTimelineClick(index);
    }
  };

  return (
    <div className={cn("relative w-full", className)}>
      {/* Timeline container */}
      <div
        ref={containerRef}
        className="h-full overflow-x-auto overflow-y-visible scrollbar-thin scrollbar-thumb-primary/20 scrollbar-track-transparent hover:scrollbar-thumb-primary/30 pb-6 pt-3 px-3 rounded-xl bg-card/50 backdrop-blur-sm border border-border/20"
        style={{ scrollbarWidth: 'thin' }}
      >
        <div className="flex items-stretch gap-3 min-w-max h-full pb-4">
          {forecastPoints.map((point, index) => {
            const weather = weatherData[index];
            const isSelected = index === selectedMarker;

            return (
              <TimelineItem
                key={`timeline-${index}`}
                point={point}
                weather={weather}
                isSelected={isSelected}
                onClick={() => handleItemClick(index)}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
}

export default ModernTimeline;
