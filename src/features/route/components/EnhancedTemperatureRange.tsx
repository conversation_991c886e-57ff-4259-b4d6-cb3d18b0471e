'use client';

/**
 * EnhancedTemperatureRange Component
 * 
 * This component displays an enhanced temperature range visualization
 * following iOS 19 design principles.
 */

import React, { useMemo } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { WeatherData } from '@/features/weather/types';
import { Thermometer } from 'lucide-react';

interface TemperatureRangeProps {
  /** Weather data points along the route */
  weatherData: WeatherData[];
  /** Optional className for styling */
  className?: string;
  /** Whether to show the temperature gradient */
  showGradient?: boolean;
  /** Whether to show the average temperature marker */
  showAverage?: boolean;
  /** Whether to show labels */
  showLabels?: boolean;
  /** Whether to show a loading state */
  loading?: boolean;
}

/**
 * A component that displays an enhanced temperature range visualization
 * Following iOS 19 design principles
 */
export function EnhancedTemperatureRange({
  weatherData,
  className,
  showGradient = true,
  showAverage = true,
  showLabels = true,
  loading = false,
}: TemperatureRangeProps) {
  // If there's no data or loading, show a loading state
  if (!weatherData || weatherData.length === 0 || loading) {
    return (
      <Card className={cn('overflow-hidden', className)} variant="glass">
        <CardHeader>
          <CardTitle>Temperature Range</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="w-full flex items-center justify-center py-8">
            <div className="w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate temperature statistics
  const temperatures = weatherData.map(data => data.temperature);
  const minTemp = Math.min(...temperatures);
  const maxTemp = Math.max(...temperatures);
  const avgTemp = temperatures.reduce((sum, temp) => sum + temp, 0) / temperatures.length;

  // Temperature range visualization data
  const temperatureRange = useMemo(() => {
    const range = maxTemp - minTemp;
    const segments = 10; // Number of segments in the visualization
    const segmentWidth = range / segments;
    
    return {
      range,
      segments,
      segmentWidth,
      min: minTemp,
      max: maxTemp,
      avg: avgTemp
    };
  }, [minTemp, maxTemp, avgTemp]);

  return (
    <Card className={cn('overflow-hidden', className)} variant="glass">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Temperature Range</CardTitle>
          <div className="flex items-center gap-2">
            <Thermometer className="h-4 w-4 text-blue-500" />
            <span className="text-sm font-medium text-blue-500">{Math.round(minTemp)}°C</span>
            <span className="text-sm">-</span>
            <span className="text-sm font-medium text-rose-500">{Math.round(maxTemp)}°C</span>
            <Thermometer className="h-4 w-4 text-rose-500" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Temperature Range Visualization */}
        <div className="space-y-4">
          <div className="h-4 w-full bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden flex">
            {Array.from({ length: temperatureRange.segments }).map((_, i) => {
              // Calculate temperature for this segment
              const segTemp = minTemp + (i * temperatureRange.segmentWidth);
              
              // Determine color based on temperature
              let bgColor = '';
              if (segTemp < 0) bgColor = 'bg-blue-500';
              else if (segTemp < 10) bgColor = 'bg-blue-400';
              else if (segTemp < 20) bgColor = 'bg-green-400';
              else if (segTemp < 30) bgColor = 'bg-yellow-400';
              else bgColor = 'bg-rose-500';
              
              return (
                <div 
                  key={`temp-seg-${i}`} 
                  className={`h-full flex-1 ${bgColor}`}
                  style={{ 
                    opacity: 0.7 + (i / temperatureRange.segments) * 0.3 
                  }}
                />
              );
            })}
          </div>
          
          {/* Average temperature marker */}
          {showAverage && (
            <div className="relative h-0">
              <div 
                className="absolute top-[-12px] w-1.5 h-5 bg-white border border-gray-400 rounded-sm shadow-sm"
                style={{ 
                  left: `${((avgTemp - minTemp) / temperatureRange.range) * 100}%`,
                  transform: 'translateX(-50%)'
                }}
              />
            </div>
          )}
          
          {showLabels && (
            <div className="flex justify-between mt-1">
              <span className="text-xs text-blue-500 font-medium">Cold</span>
              <span className="text-xs text-muted-foreground">Average: {Math.round(avgTemp)}°C</span>
              <span className="text-xs text-rose-500 font-medium">Hot</span>
            </div>
          )}
          
          {/* Temperature points visualization */}
          <div className="mt-4 pt-4 border-t border-border/20">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium">Temperature Distribution</h4>
            </div>
            <div className="relative h-16 bg-gray-50 dark:bg-gray-800/50 rounded-lg overflow-hidden">
              {weatherData.map((data, index) => {
                const position = ((data.temperature - minTemp) / temperatureRange.range) * 100;
                
                // Determine color based on temperature
                let dotColor = '';
                if (data.temperature < 0) dotColor = 'bg-blue-500';
                else if (data.temperature < 10) dotColor = 'bg-blue-400';
                else if (data.temperature < 20) dotColor = 'bg-green-400';
                else if (data.temperature < 30) dotColor = 'bg-yellow-400';
                else dotColor = 'bg-rose-500';
                
                return (
                  <div 
                    key={`temp-point-${index}`}
                    className={`absolute w-2 h-2 rounded-full ${dotColor} shadow-sm`}
                    style={{
                      left: `${position}%`,
                      top: `${50 + (Math.random() * 30 - 15)}%`,
                      transform: 'translate(-50%, -50%)'
                    }}
                    title={`${data.temperature.toFixed(1)}°C`}
                  />
                );
              })}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default EnhancedTemperatureRange;
