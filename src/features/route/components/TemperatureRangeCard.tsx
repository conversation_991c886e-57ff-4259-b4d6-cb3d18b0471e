'use client';

/**
 * TemperatureRangeCard Component
 * 
 * This component displays the temperature range along a route.
 * It shows the minimum and maximum temperatures, as well as temperature points at different locations.
 */

import React from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { formatTemperature } from '@/utils/formatters';
import { WeatherData } from '@/types';

interface TemperaturePoint {
  time: string;
  temperature: number;
  location?: string;
  feelsLike?: number;
}

interface TemperatureRangeCardProps {
  /** Weather data points along the route */
  weatherData: WeatherData[];
  /** Optional className for styling */
  className?: string;
  /** Whether to show the temperature gradient */
  showGradient?: boolean;
  /** Whether to show temperature points */
  showPoints?: boolean;
  /** Whether to show labels */
  showLabels?: boolean;
  /** Whether to show the current temperature */
  showCurrent?: boolean;
  /** Temperature unit */
  unit?: '°C' | '°F';
  /** Whether to show a loading state */
  loading?: boolean;
}

/**
 * A component that displays the temperature range along a route
 * Following iOS 19 design principles
 */
export function TemperatureRangeCard({
  weatherData,
  className,
  showGradient = true,
  showPoints = true,
  showLabels = true,
  showCurrent = true,
  unit = '°C',
  loading = false,
}: TemperatureRangeCardProps) {
  // If there's no data or loading, show a loading state
  if (!weatherData || weatherData.length === 0 || loading) {
    return (
      <Card className={cn('overflow-hidden', className)}>
        <CardHeader>
          <CardTitle>Temperature Range</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="w-full flex items-center justify-center py-8">
            <div className="w-8 h-8 border-4 border-primary/30 border-t-primary rounded-full animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate min and max temperatures
  const temperatures = weatherData.map(data => data.temperature);
  const minTemp = Math.min(...temperatures);
  const maxTemp = Math.max(...temperatures);
  const currentTemp = weatherData[0]?.temperature;

  // Create temperature points
  const temperaturePoints: TemperaturePoint[] = weatherData.map((data, index) => {
    // Create a simple time string (this would be replaced with actual time from your data)
    const hour = new Date(data.timestamp).getHours();
    const minute = new Date(data.timestamp).getMinutes();
    const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;

    return {
      time: timeString,
      temperature: data.temperature,
      feelsLike: data.feelsLike,
      // You would add location data here if available
    };
  });

  // Calculate the position of a temperature on the range (0-100%)
  const getPositionPercent = (temp: number) => {
    const range = maxTemp - minTemp;
    if (range === 0) return 50; // Avoid division by zero
    
    const position = ((temp - minTemp) / range) * 100;
    return Math.max(0, Math.min(100, position)); // Clamp between 0-100%
  };
  
  // Get color for temperature
  const getTempColor = (temp: number) => {
    if (temp <= 0) return 'rgb(59, 130, 246)'; // Blue
    if (temp <= 10) return 'rgb(14, 165, 233)'; // Sky blue
    if (temp <= 20) return 'rgb(34, 197, 94)'; // Green
    if (temp <= 30) return 'rgb(245, 158, 11)'; // Amber
    return 'rgb(239, 68, 68)'; // Red
  };

  return (
    <Card className={cn('overflow-hidden', className)} variant="glass">
      <CardHeader>
        <CardTitle>Temperature Range</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Temperature range bar */}
          <div className="relative h-8 rounded-full overflow-hidden">
            {/* Background gradient */}
            {showGradient && (
              <div 
                className="absolute inset-0"
                style={{
                  background: `linear-gradient(to right, 
                    rgb(59, 130, 246), 
                    rgb(14, 165, 233), 
                    rgb(34, 197, 94), 
                    rgb(245, 158, 11), 
                    rgb(239, 68, 68))`
                }}
              />
            )}
            
            {/* Min and max labels */}
            {showLabels && (
              <>
                <div className="absolute bottom-full left-0 mb-1 text-xs font-medium">
                  {formatTemperature(minTemp)}
                </div>
                <div className="absolute bottom-full right-0 mb-1 text-xs font-medium">
                  {formatTemperature(maxTemp)}
                </div>
              </>
            )}
            
            {/* Current temperature indicator */}
            {showCurrent && currentTemp !== undefined && (
              <div 
                className="absolute top-0 bottom-0 w-1 bg-white shadow-md z-10"
                style={{ 
                  left: `calc(${getPositionPercent(currentTemp)}% - 2px)`,
                }}
              />
            )}
            
            {/* Temperature points */}
            {showPoints && temperaturePoints.map((point, index) => (
              <div
                key={index}
                className="absolute top-0 w-2 h-2 rounded-full bg-white shadow-sm z-5"
                style={{ 
                  left: `calc(${getPositionPercent(point.temperature)}% - 4px)`,
                  top: '50%',
                  transform: 'translateY(-50%)'
                }}
                title={`${point.time}: ${formatTemperature(point.temperature)}${point.location ? ` at ${point.location}` : ''}`}
              />
            ))}
          </div>
          
          {/* Temperature points list */}
          {showPoints && temperaturePoints.length > 0 && (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 text-sm">
              {temperaturePoints.slice(0, 6).map((point, index) => (
                <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted/50">
                  <div className="flex flex-col">
                    <span className="font-medium">{point.time}</span>
                    {point.location && (
                      <span className="text-xs text-muted-foreground">{point.location}</span>
                    )}
                  </div>
                  <div className="flex flex-col items-end">
                    <span 
                      className="font-semibold"
                      style={{ color: getTempColor(point.temperature) }}
                    >
                      {formatTemperature(point.temperature)}
                    </span>
                    {point.feelsLike !== undefined && (
                      <span className="text-xs text-muted-foreground">
                        Feels: {formatTemperature(point.feelsLike)}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

export default TemperatureRangeCard;
