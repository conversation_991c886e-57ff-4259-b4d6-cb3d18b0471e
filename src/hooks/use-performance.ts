'use client';

/**
 * Performance Optimization Hooks
 * 
 * This file provides hooks for optimizing performance on mobile devices.
 */

import { useEffect, useState, useCallback, useRef } from 'react';

/**
 * useDevicePerformance Hook
 * 
 * Detects device performance capabilities and returns optimization settings.
 */
export function useDevicePerformance() {
  const [performance, setPerformance] = useState({
    isLowEndDevice: false,
    isLowMemory: false,
    isBatteryLow: false,
    isDataSaverEnabled: false,
    isReducedMotion: false,
    connectionType: 'unknown',
    effectiveConnectionType: 'unknown',
  });
  
  useEffect(() => {
    // Check for low-end device
    const isLowEndDevice = 
      navigator.hardwareConcurrency <= 4 || 
      /Android [0-5]\./.test(navigator.userAgent) ||
      /iPhone OS [0-9]_/.test(navigator.userAgent);
    
    // Check for reduced motion preference
    const isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    // Check for data saver mode
    const isDataSaverEnabled = navigator.connection ? 
      (navigator.connection as any).saveData : false;
    
    // Check connection type
    const connectionType = navigator.connection ? 
      (navigator.connection as any).type : 'unknown';
    
    // Check effective connection type (4G, 3G, 2G, etc.)
    const effectiveConnectionType = navigator.connection ? 
      (navigator.connection as any).effectiveType : 'unknown';
    
    // Update performance state
    setPerformance({
      isLowEndDevice,
      isLowMemory: false, // Will be updated by memory event listener
      isBatteryLow: false, // Will be updated by battery API
      isDataSaverEnabled,
      isReducedMotion,
      connectionType,
      effectiveConnectionType,
    });
    
    // Check for low memory
    if ('memory' in navigator) {
      const handleMemoryPressure = (event: any) => {
        setPerformance(prev => ({
          ...prev,
          isLowMemory: event.pressure === 'critical',
        }));
      };
      
      // @ts-ignore - TypeScript doesn't know about the memory pressure API yet
      if (navigator.deviceMemory && navigator.deviceMemory < 4) {
        setPerformance(prev => ({
          ...prev,
          isLowMemory: true,
        }));
      }
      
      // Listen for memory pressure events
      if ('onmemorypressure' in window) {
        window.addEventListener('memorypressure', handleMemoryPressure);
        
        return () => {
          window.removeEventListener('memorypressure', handleMemoryPressure);
        };
      }
    }
    
    // Check battery status
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        const updateBatteryStatus = () => {
          setPerformance(prev => ({
            ...prev,
            isBatteryLow: battery.level < 0.2 && !battery.charging,
          }));
        };
        
        updateBatteryStatus();
        
        battery.addEventListener('levelchange', updateBatteryStatus);
        battery.addEventListener('chargingchange', updateBatteryStatus);
        
        return () => {
          battery.removeEventListener('levelchange', updateBatteryStatus);
          battery.removeEventListener('chargingchange', updateBatteryStatus);
        };
      }).catch(() => {
        // Battery API not available or permission denied
      });
    }
    
    // Listen for connection changes
    if (navigator.connection) {
      const connection = navigator.connection as any;
      
      const updateConnectionStatus = () => {
        setPerformance(prev => ({
          ...prev,
          isDataSaverEnabled: connection.saveData,
          connectionType: connection.type,
          effectiveConnectionType: connection.effectiveType,
        }));
      };
      
      connection.addEventListener('change', updateConnectionStatus);
      
      return () => {
        connection.removeEventListener('change', updateConnectionStatus);
      };
    }
  }, []);
  
  // Return optimization settings based on device performance
  const optimizationSettings = {
    // Reduce animation complexity or disable animations
    shouldReduceAnimations: performance.isLowEndDevice || 
      performance.isReducedMotion || 
      performance.isBatteryLow,
    
    // Reduce image quality or use placeholders
    shouldReduceImageQuality: performance.isLowEndDevice || 
      performance.isDataSaverEnabled || 
      performance.effectiveConnectionType === '2g' || 
      performance.effectiveConnectionType === 'slow-2g',
    
    // Reduce the number of items rendered at once
    shouldReduceItemCount: performance.isLowEndDevice || 
      performance.isLowMemory,
    
    // Disable background effects or parallax
    shouldDisableBackgroundEffects: performance.isLowEndDevice || 
      performance.isLowMemory || 
      performance.isBatteryLow,
    
    // Use simpler components or layouts
    shouldUseSimpleLayout: performance.isLowEndDevice || 
      performance.isLowMemory,
    
    // Lazy load non-critical content
    shouldLazyLoadContent: true,
    
    // Use lower resolution maps or disable certain map features
    shouldReduceMapQuality: performance.isLowEndDevice || 
      performance.isDataSaverEnabled || 
      performance.effectiveConnectionType === '2g' || 
      performance.effectiveConnectionType === 'slow-2g',
  };
  
  return {
    ...performance,
    ...optimizationSettings,
  };
}

/**
 * useDebounce Hook
 * 
 * Debounces a value to reduce the number of renders or API calls.
 */
export function useDebounce<T>(value: T, delay: number = 500): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);
    
    return () => {
      clearTimeout(timer);
    };
  }, [value, delay]);
  
  return debouncedValue;
}

/**
 * useThrottle Hook
 * 
 * Throttles a value to reduce the number of renders or API calls.
 */
export function useThrottle<T>(value: T, limit: number = 300): T {
  const [throttledValue, setThrottledValue] = useState<T>(value);
  const lastRan = useRef(Date.now());
  
  useEffect(() => {
    const handler = setTimeout(() => {
      if (Date.now() - lastRan.current >= limit) {
        setThrottledValue(value);
        lastRan.current = Date.now();
      }
    }, limit - (Date.now() - lastRan.current));
    
    return () => {
      clearTimeout(handler);
    };
  }, [value, limit]);
  
  return throttledValue;
}

/**
 * useIdleCallback Hook
 * 
 * Runs a callback when the browser is idle.
 */
export function useIdleCallback(callback: () => void, options?: { timeout?: number }) {
  const callbackRef = useRef(callback);
  
  useEffect(() => {
    callbackRef.current = callback;
  }, [callback]);
  
  const scheduleIdleCallback = useCallback(() => {
    if ('requestIdleCallback' in window) {
      return window.requestIdleCallback(() => callbackRef.current(), options);
    } else {
      // Fallback for browsers that don't support requestIdleCallback
      return setTimeout(() => callbackRef.current(), 1);
    }
  }, [options]);
  
  useEffect(() => {
    const id = scheduleIdleCallback();
    
    return () => {
      if ('cancelIdleCallback' in window) {
        window.cancelIdleCallback(id as number);
      } else {
        clearTimeout(id as number);
      }
    };
  }, [scheduleIdleCallback]);
}

export default {
  useDevicePerformance,
  useDebounce,
  useThrottle,
  useIdleCallback,
};
