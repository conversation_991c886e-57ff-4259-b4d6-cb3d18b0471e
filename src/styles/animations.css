/**
 * Animation Utilities
 *
 * This file defines reusable animations for the application.
 * All animations follow iOS 19 guidelines for subtle, purposeful motion.
 */

/* Base animation properties */
:root {
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 250ms;
  --animation-duration-slow: 350ms;
  --animation-duration-very-slow: 500ms;

  --animation-timing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --animation-timing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --animation-timing-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --animation-timing-linear: linear;

  --animation-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Fade animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

.animate-fade-in {
  animation: fadeIn var(--animation-duration-normal) var(--animation-timing-ease-out) forwards;
}

.animate-fade-out {
  animation: fadeOut var(--animation-duration-normal) var(--animation-timing-ease-in) forwards;
}

.animate-fade-in-slow {
  animation: fadeIn var(--animation-duration-slow) var(--animation-timing-ease-out) forwards;
}

.animate-fade-out-slow {
  animation: fadeOut var(--animation-duration-slow) var(--animation-timing-ease-in) forwards;
}

/* Slide animations */
@keyframes slideInLeft {
  from { transform: translateX(-16px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(16px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInUp {
  from { transform: translateY(16px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideInDown {
  from { transform: translateY(-16px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.animate-slide-in-left {
  animation: slideInLeft var(--animation-duration-normal) var(--animation-timing-ease-out) forwards;
}

.animate-slide-in-right {
  animation: slideInRight var(--animation-duration-normal) var(--animation-timing-ease-out) forwards;
}

.animate-slide-in-up, .animate-slide-up {
  animation: slideInUp var(--animation-duration-normal) var(--animation-timing-ease-out) forwards;
}

.animate-slide-in-down {
  animation: slideInDown var(--animation-duration-normal) var(--animation-timing-ease-out) forwards;
}

/* Scale animations */
@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes scaleOut {
  from { transform: scale(1); opacity: 1; }
  to { transform: scale(0.95); opacity: 0; }
}

.animate-scale-in, .animate-scale-up {
  animation: scaleIn var(--animation-duration-normal) var(--animation-timing-ease-out) forwards;
}

.animate-scale-out {
  animation: scaleOut var(--animation-duration-normal) var(--animation-timing-ease-in) forwards;
}

/* Pulse animation for loading states */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.6; }
  100% { opacity: 1; }
}

.animate-pulse {
  animation: pulse 1.5s var(--animation-timing-ease-in-out) infinite;
}

/* Shimmer animation for loading states */
@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s var(--animation-timing-linear) infinite;
}

.dark .animate-shimmer {
  background: linear-gradient(
    90deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(255, 255, 255, 0.1) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(0, 0, 0, 0) 100%
  );
  background-size: 200% 100%;
}

/* Staggered animations for lists */
.stagger-item:nth-child(1) { animation-delay: 0.05s; }
.stagger-item:nth-child(2) { animation-delay: 0.1s; }
.stagger-item:nth-child(3) { animation-delay: 0.15s; }
.stagger-item:nth-child(4) { animation-delay: 0.2s; }
.stagger-item:nth-child(5) { animation-delay: 0.25s; }
.stagger-item:nth-child(6) { animation-delay: 0.3s; }
.stagger-item:nth-child(7) { animation-delay: 0.35s; }
.stagger-item:nth-child(8) { animation-delay: 0.4s; }
.stagger-item:nth-child(9) { animation-delay: 0.45s; }
.stagger-item:nth-child(10) { animation-delay: 0.5s; }

/* Hover animations */
.hover-lift {
  transition: transform var(--animation-duration-fast) var(--animation-timing-ease-out),
              box-shadow var(--animation-duration-fast) var(--animation-timing-ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1);
}

.hover-scale {
  transition: transform var(--animation-duration-fast) var(--animation-timing-ease-out);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Button animations */
.button-press {
  transition: transform var(--animation-duration-fast) var(--animation-timing-ease-out);
}

.button-press:active {
  transform: scale(0.97);
}

/* Card animations */
.card-hover-effect {
  transition: transform var(--animation-duration-normal) var(--animation-timing-ease-out),
              box-shadow var(--animation-duration-normal) var(--animation-timing-ease-out);
}

.card-hover-effect:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 20px -8px rgba(0, 0, 0, 0.1);
}

/* Page transitions */
.page-enter {
  opacity: 0;
}

.page-enter-active {
  opacity: 1;
  transition: opacity var(--animation-duration-normal) var(--animation-timing-ease-out);
}

.page-exit {
  opacity: 1;
}

.page-exit-active {
  opacity: 0;
  transition: opacity var(--animation-duration-normal) var(--animation-timing-ease-in);
}
