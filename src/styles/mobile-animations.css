/**
 * Mobile Animations
 * 
 * This file contains animations for mobile-friendly components
 * following iOS 19 design principles.
 */

/* Swipe animations */
@keyframes swipe-left {
  0% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes swipe-right {
  0% {
    transform: translateX(0);
  }
  20% {
    transform: translateX(20px);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes swipe-up {
  0% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(-20px);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes swipe-down {
  0% {
    transform: translateY(0);
  }
  20% {
    transform: translateY(20px);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-swipe-left {
  animation: swipe-left 0.3s ease-in-out;
}

.animate-swipe-right {
  animation: swipe-right 0.3s ease-in-out;
}

.animate-swipe-up {
  animation: swipe-up 0.3s ease-in-out;
}

.animate-swipe-down {
  animation: swipe-down 0.3s ease-in-out;
}

/* Pull to refresh animations */
@keyframes pull-bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-pull-bounce {
  animation: pull-bounce 1s infinite ease-in-out;
}

/* Sheet animations */
@keyframes sheet-in {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes sheet-out {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(100%);
  }
}

.animate-sheet-in {
  animation: sheet-in 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-sheet-out {
  animation: sheet-out 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Toast animations */
@keyframes toast-in-right {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes toast-in-left {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(0);
  }
}

@keyframes toast-in-bottom {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

@keyframes toast-in-top {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-toast-in-right {
  animation: toast-in-right 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-toast-in-left {
  animation: toast-in-left 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-toast-in-bottom {
  animation: toast-in-bottom 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

.animate-toast-in-top {
  animation: toast-in-top 0.3s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Button press animation */
@keyframes button-press {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
  100% {
    transform: scale(1);
  }
}

.animate-button-press {
  animation: button-press 0.2s ease-in-out;
}

/* Card hover animation */
@keyframes card-lift {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-5px);
  }
}

.animate-card-lift {
  animation: card-lift 0.2s ease-in-out forwards;
}

/* Fade animations */
@keyframes fade-in {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes fade-out {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out;
}

.animate-fade-out {
  animation: fade-out 0.3s ease-in-out;
}

/* Slide animations */
@keyframes slide-in-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-down {
  0% {
    transform: translateY(-20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-left {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-up {
  animation: slide-in-up 0.3s ease-in-out;
}

.animate-slide-in-down {
  animation: slide-in-down 0.3s ease-in-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.3s ease-in-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-in-out;
}

/* Touch feedback */
.touch-feedback {
  position: relative;
  overflow: hidden;
}

.touch-feedback::after {
  content: '';
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 10%, transparent 10.01%);
  background-repeat: no-repeat;
  background-position: 50%;
  transform: scale(10, 10);
  opacity: 0;
  transition: transform 0.3s, opacity 0.5s;
}

.touch-feedback:active::after {
  transform: scale(0, 0);
  opacity: 0.3;
  transition: 0s;
}

/* iOS-style scrolling */
.ios-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.ios-scroll::-webkit-scrollbar {
  display: none;
}
