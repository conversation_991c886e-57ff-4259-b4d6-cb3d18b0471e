/**
 * SunRide Layout System - iOS 19 Inspired
 *
 * This file defines the layout styles used throughout the application.
 * It follows iOS 19 design principles with a focus on consistent spacing and responsive layouts.
 */

/* Base layout settings */
:root {
  /* Spacing scale based on 8px increments */
  --space-1: 0.25rem;  /* 4px */
  --space-2: 0.5rem;   /* 8px */
  --space-3: 0.75rem;  /* 12px */
  --space-4: 1rem;     /* 16px */
  --space-5: 1.25rem;  /* 20px */
  --space-6: 1.5rem;   /* 24px */
  --space-8: 2rem;     /* 32px */
  --space-10: 2.5rem;  /* 40px */
  --space-12: 3rem;    /* 48px */
  --space-16: 4rem;    /* 64px */
  --space-20: 5rem;    /* 80px */
  --space-24: 6rem;    /* 96px */
}

/* 12-column grid system */
.grid-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 grid grid-cols-12 gap-4 sm:gap-6 lg:gap-8;
}

/* Responsive column spans */
.col-full {
  @apply col-span-12;
}

.col-main {
  @apply col-span-12 md:col-span-8;
}

.col-sidebar {
  @apply col-span-12 md:col-span-4;
}

.col-half {
  @apply col-span-12 md:col-span-6;
}

.col-third {
  @apply col-span-12 sm:col-span-6 md:col-span-4;
}

.col-two-thirds {
  @apply col-span-12 md:col-span-8;
}

.col-quarter {
  @apply col-span-12 sm:col-span-6 lg:col-span-3;
}

/* Vertical spacing */
.vertical-spacing-sm {
  @apply gap-2 sm:gap-3;
}

.vertical-spacing-md {
  @apply gap-4 sm:gap-6;
}

.vertical-spacing-lg {
  @apply gap-6 sm:gap-8 lg:gap-10;
}

/* Section spacing */
.section-spacing {
  @apply py-8 sm:py-12 lg:py-16;
}

.section-spacing-sm {
  @apply py-4 sm:py-6 lg:py-8;
}

.section-spacing-lg {
  @apply py-12 sm:py-16 lg:py-20;
}

/* Container with max width */
.content-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.content-container-sm {
  @apply max-w-3xl mx-auto px-4 sm:px-6;
}

.content-container-lg {
  @apply max-w-screen-2xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Responsive padding */
.responsive-padding {
  @apply p-4 sm:p-6 lg:p-8;
}

.responsive-padding-x {
  @apply px-4 sm:px-6 lg:px-8;
}

.responsive-padding-y {
  @apply py-4 sm:py-6 lg:py-8;
}

/* Responsive margin */
.responsive-margin {
  @apply m-4 sm:m-6 lg:m-8;
}

.responsive-margin-x {
  @apply mx-4 sm:mx-6 lg:mx-8;
}

.responsive-margin-y {
  @apply my-4 sm:my-6 lg:my-8;
}

/* Responsive gap */
.responsive-gap {
  @apply gap-4 sm:gap-6 lg:gap-8;
}

/* Flex layouts */
.flex-center {
  @apply flex items-center justify-center;
}

.flex-between {
  @apply flex items-center justify-between;
}

.flex-start {
  @apply flex items-center justify-start;
}

.flex-end {
  @apply flex items-center justify-end;
}

.flex-col-center {
  @apply flex flex-col items-center justify-center;
}

/* Responsive flex direction */
.flex-col-to-row {
  @apply flex flex-col sm:flex-row;
}

.flex-row-to-col {
  @apply flex flex-row sm:flex-col;
}

/* iOS 19 inspired card layouts */
.card-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
}

.card-container {
  @apply bg-card rounded-xl border border-border/40 shadow-sm overflow-hidden;
}

.card-padding {
  @apply p-4 sm:p-6;
}

/* iOS 19 inspired layout components */
.ios-section {
  @apply py-6 sm:py-8 lg:py-10;
}

.ios-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.ios-card {
  @apply bg-card/80 backdrop-blur-sm rounded-xl border border-border/30 shadow-sm overflow-hidden;
}

.ios-card-padding {
  @apply p-4 sm:p-5;
}

.ios-card-header {
  @apply pb-3 mb-3 border-b border-border/30;
}

.ios-card-footer {
  @apply pt-3 mt-3 border-t border-border/30;
}
