/**
 * SunRide Theme Color System - iOS 19 Inspired Design
 * Modern, clean color palette with soft blues and neutrals
 * These variables can be used throughout the application
 */

:root {
  /* Base theme colors - Light Mode */
  --color-bg: #F5F8FC;        /* Soft Blue-tinted White */
  --color-text: #364356;      /* Deep Blue-Gray */
  --color-accent: #3B82F6;    /* Calm Blue */
  --color-card: #FFFFFF;      /* Pure White */
  --color-shadow: rgba(0, 0, 0, 0.05); /* Subtle shadow */

  /* Derived colors for UI elements */
  --color-accent-light: #60A5FA; /* Lighter blue */
  --color-accent-dark: #2563EB;  /* Darker blue */
  --color-border: #E2E8F4;       /* Soft Blue-Gray for borders */
  --color-muted: #F1F5FB;        /* Soft Blue-Gray */
  --color-muted-text: #526580;   /* Medium Blue-Gray */

  /* Semantic colors */
  --color-success: #22C55E;      /* Green */
  --color-success-light: #DCFCE7; /* Light Green */
  --color-warning: #F59E0B;      /* Amber */
  --color-warning-light: #FEF3C7; /* Light Amber */
  --color-error: #EF4444;        /* Red */
  --color-error-light: #FEE2E2;  /* Light Red */
  --color-info: #3B82F6;         /* Blue */
  --color-info-light: #DBEAFE;   /* Light Blue */

  /* Map theme colors to existing system for compatibility */
  --theme-bg: var(--color-bg);
  --theme-text: var(--color-text);
  --theme-accent: var(--color-accent);
  --theme-card: var(--color-card);
  --theme-shadow: var(--color-shadow);

  /* Map to HSL variables for Tailwind */
  --background: 210 30% 98%;    /* #F5F8FC - Soft Blue-tinted White */
  --foreground: 215 25% 27%;    /* #364356 - Deep Blue-Gray */
  --primary: 210 90% 54%;       /* #3B82F6 - Calm Blue */
  --primary-foreground: 0 0% 100%;
  --primary-light: 213 94% 68%; /* #60A5FA - Lighter blue */
  --primary-dark: 217 91% 60%;  /* #2563EB - Darker blue */

  --secondary: 215 25% 27%;     /* #364356 - Deep Blue-Gray */
  --secondary-foreground: 0 0% 100%;
  --secondary-light: 214 32% 39%; /* #4A5D78 - Lighter secondary */
  --secondary-dark: 215 33% 20%;  /* #253141 - Darker secondary */

  --accent: 35 92% 50%;         /* #F59E0B - Amber */
  --accent-foreground: 0 0% 100%;
  --accent-light: 38 92% 60%;   /* #FBBF24 - Lighter accent */
  --accent-dark: 43 96% 42%;    /* #D97706 - Darker accent */

  --card: 0 0% 100%;            /* #FFFFFF - Pure White */
  --card-foreground: 215 25% 27%; /* #364356 - Deep Blue-Gray */

  --muted: 210 40% 96%;         /* #F1F5FB - Soft Blue-Gray */
  --muted-foreground: 215 25% 40%; /* #526580 - Medium Blue-Gray */

  --border: 210 30% 92%;        /* #E2E8F4 - Soft Blue-Gray */
  --input: 210 30% 92%;         /* #E2E8F4 - Soft Blue-Gray */
  --ring: 210 90% 54%;          /* #3B82F6 - Calm Blue */

  /* Semantic colors in HSL */
  --success: 142 70% 45%;       /* #22C55E - Green */
  --success-foreground: 0 0% 100%;
  --success-light: 142 76% 95%;  /* #DCFCE7 - Light Green */
  --success-dark: 142 72% 30%;   /* #15803D - Dark Green */

  --warning: 35 92% 50%;        /* #F59E0B - Amber */
  --warning-foreground: 0 0% 100%;
  --warning-light: 48 96% 89%;   /* #FEF3C7 - Light Amber */
  --warning-dark: 43 96% 42%;    /* #D97706 - Dark Amber */

  --error: 0 84% 60%;           /* #EF4444 - Red */
  --error-foreground: 0 0% 100%;
  --error-light: 0 86% 97%;      /* #FEE2E2 - Light Red */
  --error-dark: 0 74% 42%;       /* #B91C1C - Dark Red */

  --info: 210 90% 54%;          /* #3B82F6 - Blue */
  --info-foreground: 0 0% 100%;
  --info-light: 213 94% 93%;     /* #DBEAFE - Light Blue */
  --info-dark: 217 91% 60%;      /* #2563EB - Dark Blue */

  /* Radius */
  --radius: 0.5rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* Base theme colors - Dark Mode */
    --color-bg: #161C26;        /* Deep Blue-Gray */
    --color-text: #F5F9FF;      /* Soft Blue-White */
    --color-accent: #3B82F6;    /* Calm Blue */
    --color-card: #232A36;      /* Dark Blue-Gray */
    --color-shadow: rgba(0, 0, 0, 0.2); /* Subtle shadow */

    /* Derived colors for UI elements */
    --color-accent-light: #60A5FA; /* Lighter blue */
    --color-accent-dark: #2563EB;  /* Darker blue */
    --color-border: #364356;       /* Medium Blue-Gray for borders */
    --color-muted: #2F3A4A;        /* Dark Blue-Gray */
    --color-muted-text: #B4CCEB;   /* Light Blue-Gray */

    /* Semantic colors */
    --color-success: #22C55E;      /* Green */
    --color-success-light: #132E1F; /* Dark Green background */
    --color-warning: #F59E0B;      /* Amber */
    --color-warning-light: #3A2A0D; /* Dark Amber background */
    --color-error: #EF4444;        /* Red */
    --color-error-light: #3A1212;  /* Dark Red background */
    --color-info: #3B82F6;         /* Blue */
    --color-info-light: #172554;   /* Dark Blue background */

    /* Map to HSL variables for Tailwind */
    --background: 215 28% 12%;    /* #161C26 - Deep Blue-Gray */
    --foreground: 210 40% 98%;    /* #F5F9FF - Soft Blue-White */

    --primary: 210 90% 54%;       /* #3B82F6 - Calm Blue */
    --primary-foreground: 0 0% 100%;
    --primary-light: 213 94% 68%; /* #60A5FA - Lighter blue */
    --primary-dark: 217 91% 60%;  /* #2563EB - Darker blue */

    --secondary: 215 25% 27%;     /* #364356 - Deep Blue-Gray */
    --secondary-foreground: 0 0% 100%;
    --secondary-light: 214 32% 39%; /* #4A5D78 - Lighter secondary */
    --secondary-dark: 215 33% 20%;  /* #253141 - Darker secondary */

    --accent: 35 92% 50%;         /* #F59E0B - Amber */
    --accent-foreground: 0 0% 100%;
    --accent-light: 38 92% 60%;   /* #FBBF24 - Lighter accent */
    --accent-dark: 43 96% 42%;    /* #D97706 - Darker accent */

    --card: 215 25% 18%;          /* #232A36 - Dark Blue-Gray */
    --card-foreground: 210 40% 98%; /* #F5F9FF - Soft Blue-White */

    --muted: 215 28% 24%;         /* #2F3A4A - Dark Blue-Gray */
    --muted-foreground: 210 40% 80%; /* #B4CCEB - Light Blue-Gray */

    --border: 215 25% 27%;        /* #364356 - Medium Blue-Gray */
    --input: 215 25% 27%;         /* #364356 - Medium Blue-Gray */
    --ring: 210 90% 54%;          /* #3B82F6 - Calm Blue */

    /* Semantic colors in HSL for dark mode */
    --success: 142 70% 45%;       /* #22C55E - Green */
    --success-foreground: 0 0% 100%;
    --success-light: 142 50% 12%;  /* #132E1F - Dark Green background */
    --success-dark: 142 72% 30%;   /* #15803D - Dark Green */

    --warning: 35 92% 50%;        /* #F59E0B - Amber */
    --warning-foreground: 0 0% 100%;
    --warning-light: 35 70% 14%;   /* #3A2A0D - Dark Amber background */
    --warning-dark: 43 96% 42%;    /* #D97706 - Dark Amber */

    --error: 0 84% 60%;           /* #EF4444 - Red */
    --error-foreground: 0 0% 100%;
    --error-light: 0 50% 15%;      /* #3A1212 - Dark Red background */
    --error-dark: 0 74% 42%;       /* #B91C1C - Dark Red */

    --info: 210 90% 54%;          /* #3B82F6 - Blue */
    --info-foreground: 0 0% 100%;
    --info-light: 217 91% 20%;     /* #172554 - Dark Blue background */
    --info-dark: 217 91% 60%;      /* #2563EB - Dark Blue */
  }
}

/* For next-themes or manual class-based dark mode */
.dark {
  /* Base theme colors - Dark Mode */
  --color-bg: #161C26;        /* Deep Blue-Gray */
  --color-text: #F5F9FF;      /* Soft Blue-White */
  --color-accent: #3B82F6;    /* Calm Blue */
  --color-card: #232A36;      /* Dark Blue-Gray */
  --color-shadow: rgba(0, 0, 0, 0.2); /* Subtle shadow */

  /* Derived colors for UI elements */
  --color-accent-light: #60A5FA; /* Lighter blue */
  --color-accent-dark: #2563EB;  /* Darker blue */
  --color-border: #364356;       /* Medium Blue-Gray for borders */
  --color-muted: #2F3A4A;        /* Dark Blue-Gray */
  --color-muted-text: #B4CCEB;   /* Light Blue-Gray */

  /* Semantic colors */
  --color-success: #22C55E;      /* Green */
  --color-success-light: #132E1F; /* Dark Green background */
  --color-warning: #F59E0B;      /* Amber */
  --color-warning-light: #3A2A0D; /* Dark Amber background */
  --color-error: #EF4444;        /* Red */
  --color-error-light: #3A1212;  /* Dark Red background */
  --color-info: #3B82F6;         /* Blue */
  --color-info-light: #172554;   /* Dark Blue background */

  /* Map to HSL variables for Tailwind */
  --background: 215 28% 12%;    /* #161C26 - Deep Blue-Gray */
  --foreground: 210 40% 98%;    /* #F5F9FF - Soft Blue-White */

  --primary: 210 90% 54%;       /* #3B82F6 - Calm Blue */
  --primary-foreground: 0 0% 100%;
  --primary-light: 213 94% 68%; /* #60A5FA - Lighter blue */
  --primary-dark: 217 91% 60%;  /* #2563EB - Darker blue */

  --secondary: 215 25% 27%;     /* #364356 - Deep Blue-Gray */
  --secondary-foreground: 0 0% 100%;
  --secondary-light: 214 32% 39%; /* #4A5D78 - Lighter secondary */
  --secondary-dark: 215 33% 20%;  /* #253141 - Darker secondary */

  --accent: 35 92% 50%;         /* #F59E0B - Amber */
  --accent-foreground: 0 0% 100%;
  --accent-light: 38 92% 60%;   /* #FBBF24 - Lighter accent */
  --accent-dark: 43 96% 42%;    /* #D97706 - Darker accent */

  --card: 215 25% 18%;          /* #232A36 - Dark Blue-Gray */
  --card-foreground: 210 40% 98%; /* #F5F9FF - Soft Blue-White */

  --muted: 215 28% 24%;         /* #2F3A4A - Dark Blue-Gray */
  --muted-foreground: 210 40% 80%; /* #B4CCEB - Light Blue-Gray */

  --border: 215 25% 27%;        /* #364356 - Medium Blue-Gray */
  --input: 215 25% 27%;         /* #364356 - Medium Blue-Gray */
  --ring: 210 90% 54%;          /* #3B82F6 - Calm Blue */

  /* Semantic colors in HSL for dark mode */
  --success: 142 70% 45%;       /* #22C55E - Green */
  --success-foreground: 0 0% 100%;
  --success-light: 142 50% 12%;  /* #132E1F - Dark Green background */
  --success-dark: 142 72% 30%;   /* #15803D - Dark Green */

  --warning: 35 92% 50%;        /* #F59E0B - Amber */
  --warning-foreground: 0 0% 100%;
  --warning-light: 35 70% 14%;   /* #3A2A0D - Dark Amber background */
  --warning-dark: 43 96% 42%;    /* #D97706 - Dark Amber */

  --error: 0 84% 60%;           /* #EF4444 - Red */
  --error-foreground: 0 0% 100%;
  --error-light: 0 50% 15%;      /* #3A1212 - Dark Red background */
  --error-dark: 0 74% 42%;       /* #B91C1C - Dark Red */

  --info: 210 90% 54%;          /* #3B82F6 - Blue */
  --info-foreground: 0 0% 100%;
  --info-light: 217 91% 20%;     /* #172554 - Dark Blue background */
  --info-dark: 217 91% 60%;      /* #2563EB - Dark Blue */
}
