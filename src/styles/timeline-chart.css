/* Timeline and Elevation Chart Styles */

/* Timeline bar */
.timeline-bar {
  position: relative;
  z-index: 1;
  background: var(--color-bg);
  padding: 0.5rem 1rem;
  border-bottom: 2px solid var(--color-border, rgba(0, 0, 0, 0.1));
}

/* Timeline container */
.timeline-container {
  position: relative;
  overflow-x: auto;
  overflow-y: hidden;
}

.timeline-container::-webkit-scrollbar {
  height: 6px;
}

.timeline-container::-webkit-scrollbar-track {
  background-color: transparent;
}

.timeline-container::-webkit-scrollbar-thumb {
  background-color: var(--chart-humidity-color, rgba(0, 194, 168, 0.5)); /* Use chart color with accent as fallback */
  border-radius: 9999px; /* rounded-full */
}

.timeline-container::-webkit-scrollbar-thumb:hover {
  background-color: var(--chart-humidity-color, rgba(0, 194, 168, 0.7)); /* Use chart color with accent as fallback */
}

/* Timeline items */
.timeline-item {
  position: relative;
  display: flex;
  flex-direction: column;
  min-height: 360px; /* Match the height in the component */
  justify-content: space-between;
  padding: 0.75rem; /* p-3 */
  margin: 0 auto 0.5rem; /* mx-auto mb-2 */
  width: 100%;
  max-width: 170px; /* Match the max width in the component */
  box-sizing: border-box;
  overflow: visible; /* Ensure content doesn't get cut off */
}

/* Distance marker at the bottom of timeline items */
.timeline-item .distance-marker {
  margin-top: auto;
  padding-top: 8px;
  border-top: 1px dashed rgba(0, 0, 0, 0.1);
  width: 100%;
  justify-content: center;
}

/* Circle-based events */
.timeline-event {
  position: relative;
  display: flex;
  align-items: center;
}

.timeline-event-circle {
  width: 1rem; /* w-4 */
  height: 1rem; /* h-4 */
  border-radius: 9999px; /* rounded-full */
  background-color: var(--chart-humidity-color, var(--color-accent)); /* Use chart color with accent as fallback */
  display: flex; /* flex */
  align-items: center; /* items-center */
  justify-content: center; /* justify-center */
  z-index: 2; /* z-2 */
}

.timeline-event-circle.active {
  width: 1.5rem; /* w-6 */
  height: 1.5rem; /* h-6 */
  ring: 2px solid var(--chart-humidity-color, var(--color-accent)); /* ring-2 ring-accent */
  ring-offset: 2px; /* ring-offset-2 */
}

.timeline-event-connector {
  position: absolute; /* absolute */
  height: 0.125rem; /* h-0.5 */
  background-color: var(--color-border); /* bg-border */
  left: 0; /* left-0 */
  right: 0; /* right-0 */
  top: 50%; /* top-1/2 */
  transform: translateY(-50%); /* -translate-y-1/2 */
  z-index: -10; /* -z-10 */
}

/* Timeline content */
.timeline-content {
  margin-top: 0.5rem; /* mt-2 */
  padding: 0.75rem; /* p-3 */
  background-color: var(--color-card); /* bg-card */
  border-radius: 0.5rem; /* rounded-lg */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); /* shadow-sm */
}

/* Timeline details container */
.timeline-details-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem; /* gap-2 */
  padding: 0.5rem 0.25rem 1rem; /* py-2 px-1 pb-4 */
  overflow-y: auto;
  position: relative;
  margin-bottom: 0.5rem; /* mb-2 */
}

/* Timeline detail items - used for weather metrics in timeline */
.timeline-detail-item {
  margin: 0 auto;
  width: 100%;
  padding: 0.5rem 0.625rem; /* py-2 px-2.5 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0.5rem; /* rounded-lg */
  transition: all 0.2s ease;
}

.timeline-detail-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.timeline-time-weather {
  display: flex;
  align-items: center;
  gap: 0.5rem; /* gap-2 */
  font-size: 0.875rem; /* text-sm */
  font-weight: 500; /* font-medium */
}

.timeline-weather-icon {
  width: 1.25rem; /* w-5 */
  height: 1.25rem; /* h-5 */
  color: var(--chart-humidity-color, var(--color-accent)); /* Use chart color with accent as fallback */
}

/* Elevation chart */
.elevation-chart-container {
  position: relative;
  overflow: hidden;
}

@media (max-width: 768px) {
  .elevation-chart-sticky {
    position: relative;
    z-index: 1;
    background-color: var(--color-bg); /* bg-background */
    padding-top: 0.5rem; /* pt-2 */
    padding-bottom: 1rem; /* pb-4 */
    /* No shadows in flat design */
  }
}

/* Chart point markers */
.chart-point-marker {
  position: absolute; /* absolute */
  width: 0.75rem; /* w-3 */
  height: 0.75rem; /* h-3 */
  background-color: #000000; /* Black for light mode */
  transform: translate(-50%, -50%); /* transform -translate-x-1/2 -translate-y-1/2 */
  z-index: 2; /* z-2 */
  border: 1px solid #FFFFFF;
}

.chart-point-marker.active {
  width: 1rem; /* w-4 */
  height: 1rem; /* h-4 */
  background-color: #000000; /* Black for light mode */
  border: 2px solid #FFFFFF;
}

/* Chart tooltip */
.chart-custom-tooltip {
  padding: 0.5rem; /* p-2 */
  background-color: var(--color-card); /* bg-card */
  border: 1px solid var(--color-border); /* border border-border */
  font-size: 0.875rem; /* text-sm */
}
