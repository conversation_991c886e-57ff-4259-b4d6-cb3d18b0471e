/**
 * Consolidated chart styles for the application
 * Includes styles from chart-styles.css, chart-fix.css, and new-charts.css
 */

/* Direct styles for chart canvas elements - Flat Design */
canvas.humidity-chart,
canvas.pressure-chart,
canvas.uv-chart,
canvas.temperature-chart,
canvas.precipitation-chart,
canvas.wind-chart,
canvas.elevation-chart,
canvas.chartjs-render-monitor,
canvas[data-chartjs] {
  background-color: transparent !important;
  border-radius: 0 !important;
  border: 1px solid var(--color-border);
}

/* Global canvas styling for Chart.js */
canvas {
  background-color: transparent !important;
}

/* Chart container styles */
.chart-container {
  background-color: transparent !important;
  border-radius: 0 !important;
  padding: 8px !important;
}

/* Force text color for chart elements */
.chart-js-text {
  fill: var(--color-text) !important;
  color: var(--color-text) !important;
  stroke: none !important;
}

/* Force line colors */
.chart-js-line {
  stroke-width: 1px !important;
}

/* Force point colors */
.chart-js-point {
  r: 4 !important; /* Point radius */
  stroke-width: 1px !important;
  stroke: var(--color-text) !important;
}

/* Chart styling */
.humidity-chart,
.pressure-chart,
.uv-chart {
  filter: none !important;
}

/* Chart text styling */
.chart-title,
.chart-label,
.chart-tick {
  /* Use theme colors from CSS variables */
  color: var(--foreground) !important;
}

/* Chart grid lines */
.chart-grid-line {
  stroke: var(--border) !important;
  stroke-width: 1px !important;
}

/* Chart points */
.chart-point {
  stroke: var(--primary) !important;
  stroke-width: 2px !important;
  r: 4 !important;
}

/* Chart lines */
.chart-line {
  stroke-width: 2px !important;
}

/* Chart bars */
.chart-bar {
  stroke: var(--border) !important;
  stroke-width: 1px !important;
}

/* Chart tooltips */
.chartjs-tooltip {
  background-color: var(--card) !important;
  color: var(--card-foreground) !important;
  border: 1px solid var(--border) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
  border-radius: 6px !important;
  padding: 8px !important;
}

/* Chart legend */
.chartjs-legend {
  color: var(--foreground) !important;
}

/* Chart axis titles */
.chartjs-axis-title {
  color: var(--foreground) !important;
}

/* Chart ticks */
.chartjs-tick {
  color: var(--muted-foreground) !important;
}

/* UV Index legend colors - using risk level colors */
.uv-low {
  background-color: var(--chart-uv-low) !important;
}

.uv-moderate {
  background-color: var(--chart-uv-moderate) !important;
}

.uv-high {
  background-color: var(--chart-uv-high) !important;
}

.uv-very-high {
  background-color: var(--chart-uv-very-high) !important;
}

.uv-extreme {
  background-color: var(--chart-uv-extreme) !important;
}

/* Chart tooltip styles */
.chart-tooltip {
  background-color: var(--color-card);
  padding: 0.75rem;
  border: 1px solid var(--color-border);
  max-width: 250px;
  z-index: 50;
}

.chart-tooltip-title {
  font-weight: 600;
  font-size: 0.9rem;
  color: var(--color-foreground);
  margin-bottom: 0.25rem;
}

.chart-tooltip-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-foreground);
  opacity: 0.8;
  margin-bottom: 0.25rem;
}

/* Temperature chart specific styles */
.temperature-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-temperature-color);
  margin-bottom: 0.25rem;
}

.feels-like-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-feels-like-color);
  margin-bottom: 0.25rem;
}

/* Precipitation chart specific styles */
.precipitation-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-precipitation-color);
  margin-bottom: 0.25rem;
}

.probability-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-probability-color);
  margin-bottom: 0.25rem;
}

/* Wind chart specific styles */
.wind-speed-value,
.wind-gust-value,
.wind-direction-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-wind-color);
  margin-bottom: 0.25rem;
}

/* Humidity chart specific styles */
.humidity-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-humidity-color);
  margin-bottom: 0.25rem;
}

/* Pressure chart specific styles */
.pressure-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-pressure-color);
  margin-bottom: 0.25rem;
}

/* UV Index chart specific styles */
.uv-value {
  font-size: 0.875rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
}

/* UV Index risk level specific colors */
.uv-low-text {
  color: var(--chart-uv-low);
}

.uv-moderate-text {
  color: var(--chart-uv-moderate);
}

.uv-high-text {
  color: var(--chart-uv-high);
}

.uv-very-high-text {
  color: var(--chart-uv-very-high);
}

.uv-extreme-text {
  color: var(--chart-uv-extreme);
}

/* Elevation chart specific styles */
.elevation-value {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--chart-elevation-color);
  margin-bottom: 0.25rem;
}

/* Chart wrapper with visible overflow */
.chart-wrapper-visible {
  overflow: visible !important;
}

/* Ensure chart containers have proper spacing */
.recharts-wrapper {
  overflow: visible !important;
  margin-bottom: 10px !important;
}

/* Improve axis labels */
.recharts-cartesian-axis-tick-value {
  font-weight: 500 !important;
  fill: var(--color-foreground) !important;
  font-size: 12px !important;
}

/* Ensure bottom axis labels are visible */
.recharts-xAxis .recharts-cartesian-axis-tick-value {
  transform: translateY(0) !important;
}

/* Ensure x-axis has proper spacing */
.recharts-xAxis {
  transform: translateY(5px) !important;
}

.recharts-legend-item-text {
  font-weight: 500 !important;
  color: var(--color-foreground) !important;
}

/* Ensure grid lines are visible */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: var(--chart-grid-color) !important;
  stroke-width: 1 !important;
}

/* Ensure reference lines are visible */
.recharts-reference-line line {
  stroke: var(--color-accent) !important;
  stroke-width: 1 !important;
  stroke-dasharray: 3 3 !important;
}

/* Ensure reference line labels are visible */
.recharts-reference-line-label {
  fill: var(--color-foreground) !important;
  font-weight: 500 !important;
}
