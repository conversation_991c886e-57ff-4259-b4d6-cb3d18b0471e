/**
 * SunRide Typography System - iOS 19 Inspired
 *
 * This file defines the typography styles used throughout the application.
 * It follows iOS 19 design principles with a focus on readability and hierarchy.
 */

/* Base font settings */
:root {
  /* Font family */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;

  /* Font sizes */
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */
}

html {
  font-family: var(--font-sans);
  font-size: 16px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Base heading styles */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  color: hsl(var(--foreground));
  letter-spacing: -0.025em;
  margin-bottom: 0.5em;
}

/* Base text styles */
p, span, label {
  font-weight: 400;
  line-height: 1.6;
  color: hsl(var(--foreground) / 0.9);
}

/* Typography utility classes */
.text-body {
  @apply text-base text-foreground/90 leading-relaxed;
}

.text-body-lg {
  @apply text-lg text-foreground/90 leading-relaxed;
}

.text-body-sm {
  @apply text-sm text-foreground/90 leading-relaxed;
}

.text-label {
  @apply text-sm font-medium text-muted-foreground leading-normal;
}

.text-caption {
  @apply text-xs text-muted-foreground leading-normal;
}

/* Heading styles with iOS 19 inspired typography */
h1, .h1 {
  @apply text-4xl sm:text-5xl font-bold leading-tight tracking-tight;
}

h2, .h2 {
  @apply text-3xl sm:text-4xl font-bold leading-tight tracking-tight;
}

h3, .h3 {
  @apply text-2xl sm:text-3xl font-semibold leading-snug tracking-tight;
}

h4, .h4 {
  @apply text-xl sm:text-2xl font-semibold leading-snug;
}

h5, .h5 {
  @apply text-lg sm:text-xl font-medium leading-normal;
}

h6, .h6 {
  @apply text-base sm:text-lg font-medium leading-normal;
}

/* Additional typography styles */
.text-display {
  @apply text-5xl sm:text-6xl font-bold leading-none tracking-tight;
}

.text-display-lg {
  @apply text-6xl sm:text-7xl font-bold leading-none tracking-tight;
}

.text-link {
  @apply text-primary hover:text-primary/80 hover:underline transition-colors duration-200;
}

/* iOS 19 inspired text styles */
.text-title {
  @apply text-2xl font-semibold leading-tight tracking-tight text-foreground;
}

.text-subtitle {
  @apply text-lg font-medium leading-snug text-foreground/90;
}

.text-overline {
  @apply text-xs font-medium uppercase tracking-widest text-muted-foreground;
}

.text-mono {
  @apply font-mono text-sm leading-normal;
}

/* Text color utilities */
.text-primary {
  @apply text-primary;
}

.text-secondary {
  @apply text-secondary;
}

.text-muted {
  @apply text-muted-foreground;
}

.text-success {
  @apply text-green-500;
}

.text-warning {
  @apply text-amber-500;
}

.text-error {
  @apply text-red-500;
}

.text-info {
  @apply text-blue-500;
}
