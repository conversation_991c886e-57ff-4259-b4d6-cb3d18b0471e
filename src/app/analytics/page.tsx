'use client';

/**
 * Analytics Page
 * 
 * This page displays analytics data for the application.
 */

import React from 'react';
import { PageLayout } from '@/components/layout';
import { AnalyticsDashboard } from '@/components/analytics/analytics-dashboard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Activity, 
  Clock, 
  Eye, 
  MousePointer, 
  AlertTriangle, 
  Zap,
  RefreshCw,
  Download,
  Trash,
  Search,
  Filter,
  LayoutDashboard,
  Settings,
  HelpCircle
} from 'lucide-react';
import { PerformanceMonitor } from '@/components/performance/performance-monitor';

/**
 * Analytics Page
 */
export default function AnalyticsPage() {
  return (
    <PageLayout
      title="Analytics"
      description="View and analyze application usage and performance data"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
            <p className="text-muted-foreground">
              View and analyze application usage and performance data
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <HelpCircle className="h-4 w-4 mr-2" />
              Help
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
        
        <Tabs defaultValue="dashboard">
          <TabsList className="w-full border-b border-border/20 rounded-none justify-start px-4 mb-6">
            <TabsTrigger value="dashboard" className="data-[state=active]:bg-muted/50">
              <LayoutDashboard className="h-4 w-4 mr-2" />
              Dashboard
            </TabsTrigger>
            <TabsTrigger value="performance" className="data-[state=active]:bg-muted/50">
              <Zap className="h-4 w-4 mr-2" />
              Performance
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-muted/50">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </TabsTrigger>
          </TabsList>
          
          {/* Dashboard Tab */}
          <TabsContent value="dashboard">
            <AnalyticsDashboard 
              autoRefresh={true}
              refreshInterval={10000}
              showControls={true}
              showFilters={true}
              showExport={true}
              showClear={true}
              showSearch={true}
            />
          </TabsContent>
          
          {/* Performance Tab */}
          <TabsContent value="performance">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Monitor</CardTitle>
                  <CardDescription>
                    Real-time performance monitoring for the application
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="h-[500px] flex items-center justify-center border border-border/20 rounded-lg">
                    <PerformanceMonitor defaultVisible={true} position="top-right" />
                    <div className="text-center">
                      <p className="text-muted-foreground">
                        Performance monitor is displayed in the top right corner
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Web Vitals</CardTitle>
                    <CardDescription>
                      Core Web Vitals metrics for the application
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded-full bg-green-500/10">
                              <Zap className="h-4 w-4 text-green-500" />
                            </div>
                            <span className="font-medium">LCP (Largest Contentful Paint)</span>
                          </div>
                          <span>2.1s</span>
                        </div>
                        <div className="w-full bg-muted/30 rounded-full h-2">
                          <div
                            className="bg-green-500 rounded-full h-2"
                            style={{ width: '60%' }}
                          />
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Good: &lt;2.5s, Needs Improvement: 2.5s-4s, Poor: &gt;4s
                        </p>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded-full bg-green-500/10">
                              <MousePointer className="h-4 w-4 text-green-500" />
                            </div>
                            <span className="font-medium">FID (First Input Delay)</span>
                          </div>
                          <span>65ms</span>
                        </div>
                        <div className="w-full bg-muted/30 rounded-full h-2">
                          <div
                            className="bg-green-500 rounded-full h-2"
                            style={{ width: '40%' }}
                          />
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Good: &lt;100ms, Needs Improvement: 100ms-300ms, Poor: &gt;300ms
                        </p>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded-full bg-green-500/10">
                              <Activity className="h-4 w-4 text-green-500" />
                            </div>
                            <span className="font-medium">CLS (Cumulative Layout Shift)</span>
                          </div>
                          <span>0.05</span>
                        </div>
                        <div className="w-full bg-muted/30 rounded-full h-2">
                          <div
                            className="bg-green-500 rounded-full h-2"
                            style={{ width: '30%' }}
                          />
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Good: &lt;0.1, Needs Improvement: 0.1-0.25, Poor: &gt;0.25
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader>
                    <CardTitle>Resource Usage</CardTitle>
                    <CardDescription>
                      Resource usage metrics for the application
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded-full bg-blue-500/10">
                              <Clock className="h-4 w-4 text-blue-500" />
                            </div>
                            <span className="font-medium">JavaScript Execution Time</span>
                          </div>
                          <span>245ms</span>
                        </div>
                        <div className="w-full bg-muted/30 rounded-full h-2">
                          <div
                            className="bg-blue-500 rounded-full h-2"
                            style={{ width: '45%' }}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded-full bg-blue-500/10">
                              <BarChart className="h-4 w-4 text-blue-500" />
                            </div>
                            <span className="font-medium">Memory Usage</span>
                          </div>
                          <span>32MB</span>
                        </div>
                        <div className="w-full bg-muted/30 rounded-full h-2">
                          <div
                            className="bg-blue-500 rounded-full h-2"
                            style={{ width: '35%' }}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        <div className="flex justify-between">
                          <div className="flex items-center gap-2">
                            <div className="p-1 rounded-full bg-blue-500/10">
                              <LineChart className="h-4 w-4 text-blue-500" />
                            </div>
                            <span className="font-medium">Network Requests</span>
                          </div>
                          <span>24</span>
                        </div>
                        <div className="w-full bg-muted/30 rounded-full h-2">
                          <div
                            className="bg-blue-500 rounded-full h-2"
                            style={{ width: '55%' }}
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
          
          {/* Settings Tab */}
          <TabsContent value="settings">
            <Card>
              <CardHeader>
                <CardTitle>Analytics Settings</CardTitle>
                <CardDescription>
                  Configure analytics settings for the application
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Enable Analytics</h3>
                      <p className="text-sm text-muted-foreground">
                        Enable or disable analytics tracking
                      </p>
                    </div>
                    <div className="flex items-center h-6">
                      <input
                        type="checkbox"
                        id="enable-analytics"
                        className="h-4 w-4"
                        defaultChecked
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Track Page Views</h3>
                      <p className="text-sm text-muted-foreground">
                        Track page views and navigation
                      </p>
                    </div>
                    <div className="flex items-center h-6">
                      <input
                        type="checkbox"
                        id="track-page-views"
                        className="h-4 w-4"
                        defaultChecked
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Track Errors</h3>
                      <p className="text-sm text-muted-foreground">
                        Track errors and exceptions
                      </p>
                    </div>
                    <div className="flex items-center h-6">
                      <input
                        type="checkbox"
                        id="track-errors"
                        className="h-4 w-4"
                        defaultChecked
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Track Performance</h3>
                      <p className="text-sm text-muted-foreground">
                        Track performance metrics
                      </p>
                    </div>
                    <div className="flex items-center h-6">
                      <input
                        type="checkbox"
                        id="track-performance"
                        className="h-4 w-4"
                        defaultChecked
                      />
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Track User Interactions</h3>
                      <p className="text-sm text-muted-foreground">
                        Track user interactions and events
                      </p>
                    </div>
                    <div className="flex items-center h-6">
                      <input
                        type="checkbox"
                        id="track-user-interactions"
                        className="h-4 w-4"
                        defaultChecked
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}
