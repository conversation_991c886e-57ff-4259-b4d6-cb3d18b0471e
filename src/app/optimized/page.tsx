/**
 * Optimized Server-Side Rendering Page
 * 
 * This page demonstrates server-side rendering optimizations.
 */

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  getUserAgentInfo, 
  getRequestInfo, 
  cachedFetch, 
  optimizeImagesForDevice 
} from '@/lib/ssr-utils';
import { headers } from 'next/headers';

/**
 * Optimized page props
 */
interface OptimizedPageProps {
  /** Search params */
  searchParams: { [key: string]: string | string[] | undefined };
}

/**
 * Weather data
 */
interface WeatherData {
  /** Location */
  location: string;
  /** Temperature */
  temperature: number;
  /** Weather condition */
  condition: string;
  /** Weather icon */
  icon: string;
}

/**
 * Get weather data
 */
async function getWeatherData(location: string): Promise<WeatherData> {
  // Simulate API call with cached fetch
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Return mock data
  return {
    location,
    temperature: Math.floor(Math.random() * 30) + 10,
    condition: ['Sunny', 'Cloudy', 'Rainy', 'Snowy'][Math.floor(Math.random() * 4)],
    icon: ['☀️', '☁️', '🌧️', '❄️'][Math.floor(Math.random() * 4)],
  };
}

/**
 * Get images
 */
function getImages() {
  return [
    {
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
      width: 1920,
      height: 1080,
      alt: 'Mountain landscape',
    },
    {
      src: 'https://images.unsplash.com/photo-1454496522488-7a8e488e8606',
      width: 1920,
      height: 1080,
      alt: 'Mountain range',
    },
    {
      src: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
      width: 1920,
      height: 1080,
      alt: 'Mountain view',
    },
  ];
}

/**
 * Optimized page
 */
export default async function OptimizedPage({ searchParams }: OptimizedPageProps) {
  // Get user agent info
  const headersList = headers();
  const userAgent = headersList.get('user-agent') || '';
  const userAgentInfo = getUserAgentInfo(userAgent);
  
  // Get request info
  const requestInfo = getRequestInfo();
  
  // Get location from search params or default to 'New York'
  const location = typeof searchParams.location === 'string' 
    ? searchParams.location 
    : 'New York';
  
  // Get weather data
  const weatherData = await getWeatherData(location);
  
  // Get images and optimize them for the device
  const images = getImages();
  const optimizedImages = optimizeImagesForDevice(
    images,
    requestInfo.userAgent.deviceType
  );
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Server-Side Rendering Optimizations</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div className="bg-card rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Device Information</h2>
          <div className="space-y-2">
            <p><strong>Device Type:</strong> {requestInfo.userAgent.deviceType}</p>
            <p><strong>Browser:</strong> {requestInfo.userAgent.browserType}</p>
            <p><strong>OS:</strong> {requestInfo.userAgent.osType}</p>
            <p><strong>Mobile:</strong> {requestInfo.userAgent.isMobile ? 'Yes' : 'No'}</p>
            <p><strong>Tablet:</strong> {requestInfo.userAgent.isTablet ? 'Yes' : 'No'}</p>
            <p><strong>Desktop:</strong> {requestInfo.userAgent.isDesktop ? 'Yes' : 'No'}</p>
            <p><strong>Bot:</strong> {requestInfo.userAgent.isBot ? 'Yes' : 'No'}</p>
          </div>
        </div>
        
        <div className="bg-card rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">User Preferences</h2>
          <div className="space-y-2">
            <p><strong>Data Saver:</strong> {requestInfo.isDataSaverEnabled ? 'Enabled' : 'Disabled'}</p>
            <p><strong>Reduced Motion:</strong> {requestInfo.isReducedMotionEnabled ? 'Enabled' : 'Disabled'}</p>
            <p><strong>High Contrast:</strong> {requestInfo.isHighContrastEnabled ? 'Enabled' : 'Disabled'}</p>
            <p><strong>Dark Mode:</strong> {requestInfo.isDarkModeEnabled ? 'Enabled' : 'Disabled'}</p>
            <p><strong>Light Mode:</strong> {requestInfo.isLightModeEnabled ? 'Enabled' : 'Disabled'}</p>
            <p><strong>Low-End Device:</strong> {requestInfo.isLowEndDevice ? 'Yes' : 'No'}</p>
          </div>
        </div>
      </div>
      
      <div className="bg-card rounded-lg shadow-sm p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Weather Information</h2>
        <div className="flex items-center gap-4">
          <div className="text-4xl">{weatherData.icon}</div>
          <div>
            <p className="text-2xl font-bold">{weatherData.location}</p>
            <p className="text-xl">{weatherData.temperature}°C, {weatherData.condition}</p>
          </div>
        </div>
        <div className="mt-4">
          <form className="flex gap-2">
            <input
              type="text"
              name="location"
              placeholder="Enter location"
              defaultValue={location}
              className="flex-1 px-4 py-2 rounded-md border border-border bg-background"
            />
            <button
              type="submit"
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
            >
              Search
            </button>
          </form>
        </div>
      </div>
      
      <div className="space-y-6 mb-8">
        <h2 className="text-xl font-semibold">Optimized Images</h2>
        <p className="text-muted-foreground">
          These images are automatically optimized based on your device type.
          {requestInfo.userAgent.deviceType === 'mobile' && ' Smaller images are loaded on mobile devices.'}
          {requestInfo.userAgent.deviceType === 'tablet' && ' Medium-sized images are loaded on tablet devices.'}
          {requestInfo.userAgent.deviceType === 'desktop' && ' Full-sized images are loaded on desktop devices.'}
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {optimizedImages.map((image, index) => (
            <div key={index} className="rounded-lg overflow-hidden">
              <Image
                src={image.src}
                width={image.width}
                height={image.height}
                alt={images[index].alt}
                className="w-full h-auto"
                priority={index === 0}
              />
              <div className="p-2 bg-muted/20 text-xs">
                <p><strong>Width:</strong> {image.width}px</p>
                <p><strong>Height:</strong> {image.height}px</p>
                <p><strong>Original Width:</strong> {images[index].width}px</p>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="bg-card rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-semibold mb-4">Server-Side Rendering Features</h2>
        <ul className="list-disc list-inside space-y-2">
          <li>Device detection and optimization</li>
          <li>User preference detection (dark mode, reduced motion, etc.)</li>
          <li>Cached data fetching</li>
          <li>Image optimization based on device type</li>
          <li>Responsive design based on device capabilities</li>
          <li>Performance optimization for low-end devices</li>
        </ul>
        
        <div className="mt-6 flex gap-4">
          <Link
            href="/"
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md"
          >
            Back to Home
          </Link>
          <Link
            href="/showcase"
            className="px-4 py-2 bg-muted text-muted-foreground rounded-md"
          >
            View Showcase
          </Link>
        </div>
      </div>
    </div>
  );
}
