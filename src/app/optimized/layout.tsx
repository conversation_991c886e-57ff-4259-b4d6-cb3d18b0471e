/**
 * Optimized Page Layout
 * 
 * This layout is optimized for server-side rendering.
 */

import React from 'react';
import { Metadata } from 'next';
import { getRequestInfo } from '@/lib/ssr-utils';

/**
 * Generate metadata
 */
export async function generateMetadata(): Promise<Metadata> {
  // Get request info
  const requestInfo = getRequestInfo();
  
  // Optimize metadata based on device type
  const title = 'Optimized SSR Page';
  const description = 'This page demonstrates server-side rendering optimizations.';
  
  return {
    title,
    description,
    // Add viewport settings based on device type
    viewport: {
      width: 'device-width',
      initialScale: 1,
      maximumScale: requestInfo.userAgent.isMobile ? 5 : 1,
      userScalable: true,
    },
    // Add theme color based on color scheme preference
    themeColor: requestInfo.isDarkModeEnabled ? '#111' : '#ffffff',
    // Add additional metadata
    openGraph: {
      title,
      description,
      type: 'website',
      // Optimize image based on device type
      images: [
        {
          url: requestInfo.userAgent.isMobile
            ? '/images/og-mobile.jpg'
            : '/images/og-desktop.jpg',
          width: requestInfo.userAgent.isMobile ? 640 : 1200,
          height: requestInfo.userAgent.isMobile ? 320 : 630,
          alt: title,
        },
      ],
    },
    // Add Twitter card
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
  };
}

/**
 * Layout props
 */
interface LayoutProps {
  /** Children components */
  children: React.ReactNode;
}

/**
 * Optimized layout
 */
export default function OptimizedLayout({ children }: LayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      <header className="bg-card border-b border-border/20 py-4">
        <div className="container mx-auto px-4">
          <h1 className="text-2xl font-bold">Optimized SSR</h1>
        </div>
      </header>
      
      <main>
        {children}
      </main>
      
      <footer className="bg-card border-t border-border/20 py-4 mt-8">
        <div className="container mx-auto px-4 text-center text-sm text-muted-foreground">
          <p>This page is optimized for server-side rendering.</p>
        </div>
      </footer>
    </div>
  );
}
