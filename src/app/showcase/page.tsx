'use client';

/**
 * Showcase Page
 * 
 * This page showcases all the improvements we've made to the application.
 */

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { 
  ResponsiveLayoutWrapper, 
  ResponsiveContainer, 
  ResponsiveGrid, 
  ResponsiveSection,
  CardGrid
} from '@/components/layout';
import { 
  SwipeContainer,
  PullToRefresh,
  MobileTabs,
  MobileAccordion,
  BottomSheet,
  ActionSheet,
  MobileGallery,
  useToast
} from '@/components/ui/mobile-ui';
import { 
  SkipLink, 
  VisuallyHidden, 
  FocusTrap, 
  LiveRegion, 
  AccessibleIcon,
  useAnnounce
} from '@/components/ui/accessibility';
import { LazyImage, LazyComponent, VirtualList } from '@/components/ui/lazy-load';
import { usePerformance } from '@/components/performance/performance-provider';
import { PerformanceMonitor } from '@/components/performance/performance-monitor';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Smartphone, 
  Layout, 
  Zap, 
  Accessibility, 
  ChevronRight,
  Palette,
  Component,
  Layers,
  FileCode,
  BookOpen,
  Home,
  Settings,
  User,
  Bell,
  MessageSquare,
  Trash,
  Share,
  Edit,
  Copy,
  Download,
  Info,
  RefreshCw,
  Check,
  X,
  ArrowRight
} from 'lucide-react';

// Import our utilities
import { useOptimizedImage } from '@/lib/image-utils';
import { createPerformanceMark, createPerformanceMeasure } from '@/lib/performance-utils';
import { useBreakpoint, useMobile, useTablet, useDesktop } from '@/lib/responsive-utils';
import { useKeyboardNavigation, useReducedMotion, useHighContrast } from '@/lib/accessibility-utils';

/**
 * Showcase Page
 */
export default function ShowcasePage() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [isActionSheetOpen, setIsActionSheetOpen] = useState(false);
  const { showToast } = useToast();
  const performance = usePerformance();
  const { announce } = useAnnounce();
  const breakpoint = useBreakpoint();
  const isMobile = useMobile();
  const isTablet = useTablet();
  const isDesktop = useDesktop();
  const isKeyboardUser = useKeyboardNavigation();
  const prefersReducedMotion = useReducedMotion();
  const isHighContrast = useHighContrast();
  
  // Create performance marks
  useEffect(() => {
    createPerformanceMark('showcase-page-loaded', 'Showcase page loaded');
    
    return () => {
      createPerformanceMeasure(
        'showcase-page-duration',
        'showcase-page-loaded',
        'navigation-start',
        'Time spent on showcase page'
      );
    };
  }, []);
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    // Simulate a network request
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsRefreshing(false);
    showToast({
      type: 'success',
      title: 'Refreshed',
      message: 'Content has been refreshed',
      duration: 3000,
    });
  };
  
  // Example data for components
  const tabItems = [
    {
      id: 'mobile',
      label: 'Mobile',
      icon: <Smartphone className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Mobile Components</h3>
          <p className="text-muted-foreground mb-4">Mobile-friendly components with touch interactions.</p>
          <div className="space-y-4">
            <SwipeContainer
              className="h-32 bg-muted/30 rounded-lg flex items-center justify-center"
              onSwipeLeft={() => showToast({ type: 'info', message: 'Swiped left' })}
              onSwipeRight={() => showToast({ type: 'info', message: 'Swiped right' })}
            >
              <div className="text-center">
                <p className="font-medium">Swipe me</p>
                <p className="text-sm text-muted-foreground">Try swiping left or right</p>
              </div>
            </SwipeContainer>
            <div className="flex flex-wrap gap-2">
              <Button onClick={() => setIsBottomSheetOpen(true)}>
                Open Bottom Sheet
              </Button>
              <Button onClick={() => setIsActionSheetOpen(true)}>
                Open Action Sheet
              </Button>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'responsive',
      label: 'Responsive',
      icon: <Layout className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Responsive Layout</h3>
          <p className="text-muted-foreground mb-4">Layout components that adapt to different screen sizes.</p>
          <div className="space-y-4">
            <div className="border border-border/20 rounded-lg p-4">
              <p className="text-sm text-muted-foreground mb-2">Current breakpoint: <span className="font-medium">{breakpoint}</span></p>
              <p className="text-sm text-muted-foreground mb-2">Device type: <span className="font-medium">{isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}</span></p>
              <ResponsiveGrid
                mobileColumns={1}
                tabletColumns={2}
                desktopColumns={3}
                gap="md"
              >
                {[1, 2, 3].map((item) => (
                  <div
                    key={item}
                    className="bg-muted/30 border border-border/20 rounded-lg p-4 h-24 flex items-center justify-center"
                  >
                    Item {item}
                  </div>
                ))}
              </ResponsiveGrid>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'performance',
      label: 'Performance',
      icon: <Zap className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Performance Optimization</h3>
          <p className="text-muted-foreground mb-4">Components and techniques to improve performance.</p>
          <div className="space-y-4">
            <LazyImage
              src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4"
              alt="Mountain landscape"
              width={800}
              height={400}
              className="w-full h-64 rounded-lg"
            />
            <div className="border border-border/20 rounded-lg p-4">
              <p className="text-sm text-muted-foreground mb-2">Device performance:</p>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Low-end device:</span>
                  <span>{performance.isLowEndDevice ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Data saver:</span>
                  <span>{performance.isDataSaver ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Reduced motion:</span>
                  <span>{performance.hasReducedMotion ? 'Yes' : 'No'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'accessibility',
      label: 'Accessibility',
      icon: <Accessibility className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Accessibility</h3>
          <p className="text-muted-foreground mb-4">Components and practices to improve accessibility.</p>
          <div className="space-y-4">
            <div className="border border-border/20 rounded-lg p-4">
              <p className="text-sm text-muted-foreground mb-2">Accessibility preferences:</p>
              <div className="space-y-1 text-sm">
                <div className="flex justify-between">
                  <span>Keyboard user:</span>
                  <span>{isKeyboardUser ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Reduced motion:</span>
                  <span>{prefersReducedMotion ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span>High contrast:</span>
                  <span>{isHighContrast ? 'Yes' : 'No'}</span>
                </div>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={() => {
                  announce('This is a polite announcement', 'polite');
                  showToast({ type: 'info', message: 'Announcement made' });
                }}
              >
                <Info className="h-4 w-4 mr-2" />
                Announce to Screen Readers
              </Button>
              <Button variant="outline">
                <Bell className="h-4 w-4 mr-2" />
                Notifications
                <VisuallyHidden>(5 unread)</VisuallyHidden>
              </Button>
            </div>
          </div>
        </div>
      ),
    },
  ];
  
  const accordionItems = [
    {
      id: 'item1',
      title: 'Mobile Components',
      content: (
        <div>
          <p className="text-muted-foreground">
            Mobile-friendly components with touch interactions, swipe gestures, and responsive layouts.
          </p>
        </div>
      ),
    },
    {
      id: 'item2',
      title: 'Responsive Layout',
      content: (
        <div>
          <p className="text-muted-foreground">
            Layout components that adapt to different screen sizes and device capabilities.
          </p>
        </div>
      ),
    },
    {
      id: 'item3',
      title: 'Performance Optimization',
      content: (
        <div>
          <p className="text-muted-foreground">
            Techniques to improve performance, especially on mobile devices with limited resources.
          </p>
        </div>
      ),
    },
    {
      id: 'item4',
      title: 'Accessibility',
      content: (
        <div>
          <p className="text-muted-foreground">
            Components and practices to improve accessibility for users with disabilities.
          </p>
        </div>
      ),
    },
  ];
  
  const actionSheetItems = [
    {
      id: 'edit',
      label: 'Edit',
      icon: <Edit className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Edit clicked' }),
    },
    {
      id: 'share',
      label: 'Share',
      icon: <Share className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Share clicked' }),
    },
    {
      id: 'copy',
      label: 'Copy',
      icon: <Copy className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Copy clicked' }),
    },
    {
      id: 'download',
      label: 'Download',
      icon: <Download className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Download clicked' }),
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <Trash className="h-4 w-4" />,
      destructive: true,
      onClick: () => showToast({ type: 'error', message: 'Delete clicked' }),
    },
  ];
  
  const galleryImages = [
    {
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
      alt: 'Mountain landscape',
    },
    {
      src: 'https://images.unsplash.com/photo-1454496522488-7a8e488e8606',
      alt: 'Mountain range',
    },
    {
      src: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
      alt: 'Mountain view',
    },
  ];
  
  const cardItems = [
    {
      id: 'card1',
      title: 'Mobile Components',
      description: 'Mobile-friendly components with touch interactions',
      content: (
        <div className="h-32 flex items-center justify-center">
          <Smartphone className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/examples/mobile">
            <span>View Examples</span>
          </Link>
        </Button>
      ),
    },
    {
      id: 'card2',
      title: 'Responsive Layout',
      description: 'Layout components that adapt to different screen sizes',
      content: (
        <div className="h-32 flex items-center justify-center">
          <Layout className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/examples/responsive">
            <span>View Examples</span>
          </Link>
        </Button>
      ),
    },
    {
      id: 'card3',
      title: 'Performance Optimization',
      description: 'Techniques to improve performance',
      content: (
        <div className="h-32 flex items-center justify-center">
          <Zap className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/examples/performance">
            <span>View Examples</span>
          </Link>
        </Button>
      ),
    },
    {
      id: 'card4',
      title: 'Accessibility',
      description: 'Components and practices to improve accessibility',
      content: (
        <div className="h-32 flex items-center justify-center">
          <Accessibility className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button asChild variant="outline" size="sm" className="w-full">
          <Link href="/examples/accessibility">
            <span>View Examples</span>
          </Link>
        </Button>
      ),
    },
  ];
  
  return (
    <ResponsiveLayoutWrapper
      title="Showcase"
      description="Showcase of all improvements"
      maxWidth="xl"
      padding="md"
      glass={true}
      bordered={false}
      shadowed={false}
      rounded={true}
      showTopNav={true}
      showBottomNav={true}
      navItems={[
        {
          href: '/',
          label: 'Home',
          icon: <Home className="h-4 w-4" />,
        },
        {
          href: '/examples',
          label: 'Examples',
          icon: <Component className="h-4 w-4" />,
        },
        {
          href: '/docs',
          label: 'Docs',
          icon: <FileCode className="h-4 w-4" />,
        },
        {
          href: '/showcase',
          label: 'Showcase',
          icon: <Layers className="h-4 w-4" />,
        },
      ]}
    >
      <PullToRefresh
        onRefresh={handleRefresh}
        refreshingText="Refreshing..."
        pullText="Pull to refresh"
        releaseText="Release to refresh"
      >
        <div className="space-y-6 pb-20">
          <Card>
            <CardHeader>
              <CardTitle>Showcase</CardTitle>
              <CardDescription>
                Showcase of all improvements made to the application
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                This page showcases all the improvements we've made to the application, including mobile-friendly components,
                responsive layouts, performance optimizations, and accessibility improvements.
              </p>
              <div className="flex flex-wrap gap-4 items-center justify-center">
                <div className="flex flex-col items-center gap-2">
                  <div className="w-16 h-16 rounded-full bg-blue-500/10 flex items-center justify-center">
                    <Smartphone className="h-8 w-8 text-blue-500" />
                  </div>
                  <span className="text-sm font-medium">Mobile</span>
                </div>
                <ArrowRight className="hidden sm:block h-4 w-4 text-muted-foreground" />
                <div className="flex flex-col items-center gap-2">
                  <div className="w-16 h-16 rounded-full bg-purple-500/10 flex items-center justify-center">
                    <Layout className="h-8 w-8 text-purple-500" />
                  </div>
                  <span className="text-sm font-medium">Responsive</span>
                </div>
                <ArrowRight className="hidden sm:block h-4 w-4 text-muted-foreground" />
                <div className="flex flex-col items-center gap-2">
                  <div className="w-16 h-16 rounded-full bg-amber-500/10 flex items-center justify-center">
                    <Zap className="h-8 w-8 text-amber-500" />
                  </div>
                  <span className="text-sm font-medium">Performance</span>
                </div>
                <ArrowRight className="hidden sm:block h-4 w-4 text-muted-foreground" />
                <div className="flex flex-col items-center gap-2">
                  <div className="w-16 h-16 rounded-full bg-green-500/10 flex items-center justify-center">
                    <Accessibility className="h-8 w-8 text-green-500" />
                  </div>
                  <span className="text-sm font-medium">Accessibility</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <MobileTabs
            tabs={tabItems}
            defaultTab="mobile"
            enableSwipe={true}
            showArrows={true}
            showPagination={true}
          />
          
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Accordion</h2>
            <p className="text-sm text-muted-foreground">
              A mobile-friendly accordion with touch-friendly interactions.
            </p>
            <MobileAccordion
              items={accordionItems}
              defaultOpen={['item1']}
              allowMultiple={true}
              collapseOthers={false}
              bordered={true}
              glass={true}
            />
          </div>
          
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Gallery</h2>
            <p className="text-sm text-muted-foreground">
              A mobile-friendly image gallery with swipe navigation.
            </p>
            <div className="h-64">
              <MobileGallery
                images={galleryImages}
                initialIndex={0}
                enableSwipe={true}
                showControls={true}
                showPagination={true}
                enableZoom={true}
                enableFullscreen={true}
                glass={true}
                bordered={true}
                shadowed={true}
                rounded={true}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Card Grid</h2>
            <p className="text-sm text-muted-foreground">
              A responsive grid of cards that adapts to different screen sizes.
            </p>
            <CardGrid
              items={cardItems}
              mobileColumns={1}
              tabletColumns={2}
              desktopColumns={2}
              largeDesktopColumns={4}
              gap="md"
              glass={true}
              bordered={true}
              shadowed={true}
              rounded={true}
              equalHeight={true}
            />
          </div>
          
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Toast Notifications</h2>
            <p className="text-sm text-muted-foreground">
              Mobile-friendly toast notifications.
            </p>
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'success', 
                    title: 'Success', 
                    message: 'This is a success toast notification',
                    duration: 3000,
                  })
                }
              >
                Success Toast
              </Button>
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'error', 
                    title: 'Error', 
                    message: 'This is an error toast notification',
                    duration: 3000,
                  })
                }
              >
                Error Toast
              </Button>
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'info', 
                    title: 'Info', 
                    message: 'This is an info toast notification',
                    duration: 3000,
                  })
                }
              >
                Info Toast
              </Button>
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'warning', 
                    title: 'Warning', 
                    message: 'This is a warning toast notification',
                    duration: 3000,
                  })
                }
              >
                Warning Toast
              </Button>
            </div>
          </div>
        </div>
      </PullToRefresh>
      
      {/* Bottom Sheet */}
      <BottomSheet
        isOpen={isBottomSheetOpen}
        onClose={() => setIsBottomSheetOpen(false)}
        title="Bottom Sheet"
        showCloseButton={true}
        showDragHandle={true}
      >
        <div className="p-4 space-y-4">
          <p className="text-muted-foreground">
            This is a bottom sheet component that follows iOS 19 design principles.
            It provides a mobile-friendly way to display content that slides up from the bottom of the screen.
          </p>
          <Button onClick={() => setIsBottomSheetOpen(false)} className="w-full">
            Close
          </Button>
        </div>
      </BottomSheet>
      
      {/* Action Sheet */}
      <ActionSheet
        isOpen={isActionSheetOpen}
        onClose={() => setIsActionSheetOpen(false)}
        title="Action Sheet"
        items={actionSheetItems}
        showCancel={true}
        cancelLabel="Cancel"
      />
    </ResponsiveLayoutWrapper>
  );
}
