import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import '@/styles/custom-loader.css';
import Providers from './providers';
import { Footer } from '@/components/layout/footer';
import { SkipLink } from '@/components/ui/accessibility';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'SunRide',
  description: 'Plan your routes with detailed weather forecasts along the way',
  keywords: ['weather', 'planning', 'GPX', 'route', 'cycling', 'hiking', 'forecast'],
  authors: [{ name: '<PERSON><PERSON><PERSON>' }],
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'SunRide',
  },
  formatDetection: {
    telephone: false,
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: '#111' },
  ],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* Theme initialization script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
          (function() {
            try {
              const theme = localStorage.getItem('theme') || 'system';
              const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
              document.documentElement.classList.add(theme === 'system' ? systemTheme : theme);
            } catch (e) {
              console.error('Error applying theme:', e);
              document.documentElement.classList.add('dark');
            }
          })()
        `,
          }}
        />
      </head>
      <body className={inter.className}>
        <Providers>
          {/* Add skip link for accessibility */}
          <SkipLink href="#main-content">Skip to main content</SkipLink>

          <div className="relative flex min-h-screen flex-col">
            {/* Main content with ID for skip link */}
            <main id="main-content" className="flex-1 pb-16">
              {children}
            </main>
            <Footer />
          </div>
        </Providers>

        {/* Map button fix script */}
        <script src="/js/map-button-fix.js" defer></script>
      </body>
    </html>
  );
}
