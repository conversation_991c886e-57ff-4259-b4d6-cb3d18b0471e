'use client';

/**
 * Performance Optimization Example Page
 * 
 * This page showcases the performance optimization components in action.
 */

import React, { useState, useEffect } from 'react';
import { PageLayout } from '@/components/layout';
import { LazyImage, LazyComponent, VirtualList } from '@/components/ui/lazy-load';
import { useDevicePerformance, useDebounce, useThrottle, useIdleCallback } from '@/hooks/use-performance';
import { PerformanceProvider, usePerformance } from '@/components/performance/performance-provider';
import { PerformanceMonitor } from '@/components/performance/performance-monitor';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { 
  Zap, 
  Image as ImageIcon, 
  List, 
  Clock, 
  Cpu, 
  BarChart, 
  Search,
  RefreshCw,
  Smartphone,
  Tablet,
  Monitor,
  Battery,
  Wifi,
  Memory,
  Save,
  Loader2
} from 'lucide-react';
import { AccessibilityProvider } from '@/components/ui/accessibility';

/**
 * Performance Optimization Example Page
 */
export default function PerformanceOptimizationExamplePage() {
  return (
    <AccessibilityProvider>
      <PerformanceProvider showMonitor={true}>
        <PerformanceOptimizationExampleContent />
      </PerformanceProvider>
    </AccessibilityProvider>
  );
}

/**
 * Performance Optimization Example Content
 */
function PerformanceOptimizationExampleContent() {
  const [count, setCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  
  const debouncedSearchTerm = useDebounce(searchTerm, 500);
  const throttledCount = useThrottle(count, 300);
  const performance = usePerformance();
  
  // Generate a list of items for the VirtualList example
  const items = Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    name: `Item ${i}`,
    description: `This is item ${i}`,
  }));
  
  // Simulate search
  useEffect(() => {
    if (debouncedSearchTerm) {
      setIsLoading(true);
      
      // Simulate API call
      const timer = setTimeout(() => {
        const results = Array.from({ length: 20 }, (_, i) => 
          `Result ${i + 1} for "${debouncedSearchTerm}"`
        );
        setSearchResults(results);
        setIsLoading(false);
      }, 500);
      
      return () => clearTimeout(timer);
    } else {
      setSearchResults([]);
    }
  }, [debouncedSearchTerm]);
  
  // Use idle callback to perform non-critical tasks
  useIdleCallback(() => {
    console.log('Performing non-critical tasks during idle time');
    
    // Simulate preloading images or other resources
    const images = [
      'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
      'https://images.unsplash.com/photo-1454496522488-7a8e488e8606',
      'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
    ];
    
    images.forEach(src => {
      const img = new Image();
      img.src = src;
    });
  });
  
  return (
    <PageLayout
      title="Performance Optimization"
      description="Examples of performance optimization components and techniques"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Performance Optimization</CardTitle>
            <CardDescription>
              These components and techniques help optimize performance, especially on mobile devices.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Performance optimization is crucial for providing a good user experience, especially on mobile devices.
              These components and techniques help improve performance by reducing unnecessary renders, lazy loading content,
              and adapting to device capabilities.
            </p>
            <div className="flex flex-wrap gap-4 items-center justify-center">
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <Zap className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Fast</span>
                <span className="text-xs text-muted-foreground">Loading</span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <ImageIcon className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Lazy</span>
                <span className="text-xs text-muted-foreground">Loading</span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <List className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Virtual</span>
                <span className="text-xs text-muted-foreground">Lists</span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <Clock className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Debounce</span>
                <span className="text-xs text-muted-foreground">& Throttle</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Tabs defaultValue="device" className="w-full">
          <TabsList className="w-full border-b border-border/20 rounded-none justify-start px-4 mb-6">
            <TabsTrigger value="device" className="data-[state=active]:bg-muted/50">
              Device Performance
            </TabsTrigger>
            <TabsTrigger value="lazy" className="data-[state=active]:bg-muted/50">
              Lazy Loading
            </TabsTrigger>
            <TabsTrigger value="virtual" className="data-[state=active]:bg-muted/50">
              Virtual Lists
            </TabsTrigger>
            <TabsTrigger value="debounce" className="data-[state=active]:bg-muted/50">
              Debounce & Throttle
            </TabsTrigger>
          </TabsList>
          
          {/* Device Performance Tab */}
          <TabsContent value="device" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Device Performance</CardTitle>
                <CardDescription>
                  Detect device performance capabilities and adapt accordingly.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <p className="text-muted-foreground">
                  The useDevicePerformance hook detects device performance capabilities and returns optimization settings.
                  This allows you to adapt your application to the device's capabilities.
                </p>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Your Device Information</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">Device Type</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center gap-3">
                          {performance.isMobile ? (
                            <Smartphone className="h-8 w-8 text-primary" />
                          ) : performance.isTablet ? (
                            <Tablet className="h-8 w-8 text-primary" />
                          ) : (
                            <Monitor className="h-8 w-8 text-primary" />
                          )}
                          <div>
                            <p className="font-medium">
                              {performance.isMobile ? 'Mobile' : performance.isTablet ? 'Tablet' : 'Desktop'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {performance.isPortrait ? 'Portrait' : 'Landscape'} orientation
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">Input Methods</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${performance.hasTouch ? 'bg-green-500' : 'bg-red-500'}`} />
                            <span>Touch Screen: {performance.hasTouch ? 'Yes' : 'No'}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${performance.hasMouse ? 'bg-green-500' : 'bg-red-500'}`} />
                            <span>Mouse: {performance.hasMouse ? 'Yes' : 'No'}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className={`w-3 h-3 rounded-full ${performance.hasKeyboard ? 'bg-green-500' : 'bg-red-500'}`} />
                            <span>Keyboard: {performance.hasKeyboard ? 'Yes' : 'No'}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">Network</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center gap-3">
                          <Wifi className="h-8 w-8 text-primary" />
                          <div>
                            <p className="font-medium">
                              {performance.isOnline ? 'Online' : 'Offline'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {performance.isDataSaver ? 'Data Saver Enabled' : 'Data Saver Disabled'}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              Connection: {performance.connectionType}
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-base">Battery & Memory</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div className="flex items-center gap-3">
                            <Battery className="h-5 w-5 text-primary" />
                            <div>
                              <p className="text-sm">
                                Battery: {performance.isLowBattery ? 'Low' : 'Normal'}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <Memory className="h-5 w-5 text-primary" />
                            <div>
                              <p className="text-sm">
                                Memory: {performance.isLowMemory ? 'Low' : 'Normal'}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Optimization Settings</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="p-4 border border-border/20 rounded-lg">
                      <h4 className="font-medium mb-2">Animations</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        {performance.shouldReduceAnimations
                          ? 'Animations should be reduced or disabled'
                          : 'Full animations can be used'}
                      </p>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${performance.hasReducedMotion ? 'bg-amber-500' : 'bg-green-500'}`} />
                        <span className="text-xs">
                          {performance.hasReducedMotion ? 'Reduced Motion Enabled' : 'Reduced Motion Disabled'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4 border border-border/20 rounded-lg">
                      <h4 className="font-medium mb-2">Image Quality</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        {performance.shouldReduceImageQuality
                          ? 'Image quality should be reduced'
                          : 'Full quality images can be used'}
                      </p>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${performance.isDataSaver ? 'bg-amber-500' : 'bg-green-500'}`} />
                        <span className="text-xs">
                          {performance.isDataSaver ? 'Data Saver Enabled' : 'Data Saver Disabled'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4 border border-border/20 rounded-lg">
                      <h4 className="font-medium mb-2">Layout Complexity</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        {performance.shouldUseSimpleLayout
                          ? 'Simpler layouts should be used'
                          : 'Complex layouts can be used'}
                      </p>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${performance.isLowEndDevice ? 'bg-amber-500' : 'bg-green-500'}`} />
                        <span className="text-xs">
                          {performance.isLowEndDevice ? 'Low-End Device' : 'High-End Device'}
                        </span>
                      </div>
                    </div>
                    
                    <div className="p-4 border border-border/20 rounded-lg">
                      <h4 className="font-medium mb-2">Background Effects</h4>
                      <p className="text-sm text-muted-foreground mb-2">
                        {performance.shouldDisableBackgroundEffects
                          ? 'Background effects should be disabled'
                          : 'Background effects can be used'}
                      </p>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${performance.isLowMemory ? 'bg-amber-500' : 'bg-green-500'}`} />
                        <span className="text-xs">
                          {performance.isLowMemory ? 'Low Memory' : 'Normal Memory'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Lazy Loading Tab */}
          <TabsContent value="lazy" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Lazy Loading</CardTitle>
                <CardDescription>
                  Load content only when it's needed.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <p className="text-muted-foreground">
                  Lazy loading improves performance by loading content only when it's needed.
                  This reduces initial load time and saves bandwidth.
                </p>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Lazy Images</h3>
                  <p className="text-sm text-muted-foreground">
                    These images will only load when they enter the viewport.
                  </p>
                  <div className="space-y-4">
                    <LazyImage
                      src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4"
                      alt="Mountain landscape"
                      width={800}
                      height={400}
                      className="w-full h-64 rounded-lg"
                    />
                    <LazyImage
                      src="https://images.unsplash.com/photo-1454496522488-7a8e488e8606"
                      alt="Mountain range"
                      width={800}
                      height={400}
                      className="w-full h-64 rounded-lg"
                      loadingComponent={
                        <div className="flex items-center justify-center h-full">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      }
                    />
                    <LazyImage
                      src="https://images.unsplash.com/photo-1464822759023-fed622ff2c3b"
                      alt="Mountain view"
                      width={800}
                      height={400}
                      className="w-full h-64 rounded-lg"
                    />
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Lazy Components</h3>
                  <p className="text-sm text-muted-foreground">
                    These components will only render when they enter the viewport.
                  </p>
                  <div className="space-y-4">
                    <LazyComponent
                      className="w-full h-64 rounded-lg"
                      loadingComponent={
                        <div className="flex items-center justify-center h-full border border-border/20 rounded-lg bg-muted/20">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      }
                    >
                      <div className="w-full h-full border border-border/20 rounded-lg bg-card p-4 flex items-center justify-center">
                        <div className="text-center">
                          <h4 className="font-medium">Lazy Component 1</h4>
                          <p className="text-sm text-muted-foreground">
                            This component was loaded when it entered the viewport.
                          </p>
                        </div>
                      </div>
                    </LazyComponent>
                    <LazyComponent
                      className="w-full h-64 rounded-lg"
                      loadingComponent={
                        <div className="flex items-center justify-center h-full border border-border/20 rounded-lg bg-muted/20">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      }
                    >
                      <div className="w-full h-full border border-border/20 rounded-lg bg-card p-4 flex items-center justify-center">
                        <div className="text-center">
                          <h4 className="font-medium">Lazy Component 2</h4>
                          <p className="text-sm text-muted-foreground">
                            This component was loaded when it entered the viewport.
                          </p>
                        </div>
                      </div>
                    </LazyComponent>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Virtual Lists Tab */}
          <TabsContent value="virtual" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Virtual Lists</CardTitle>
                <CardDescription>
                  Render only the items that are visible in the viewport.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <p className="text-muted-foreground">
                  Virtual lists improve performance by rendering only the items that are visible in the viewport.
                  This is especially important for long lists with many items.
                </p>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Virtual List Example</h3>
                  <p className="text-sm text-muted-foreground">
                    This list has 1,000 items, but only renders the ones that are visible.
                  </p>
                  <div className="border border-border/20 rounded-lg">
                    <div className="p-4 border-b border-border/20 bg-muted/10">
                      <h4 className="font-medium">Virtual List</h4>
                      <p className="text-xs text-muted-foreground">
                        Scroll to see more items.
                      </p>
                    </div>
                    <div className="h-64">
                      <VirtualList
                        items={items}
                        renderItem={(item) => (
                          <div className="p-4 border-b border-border/10 last:border-b-0">
                            <div className="font-medium">{item.name}</div>
                            <div className="text-sm text-muted-foreground">{item.description}</div>
                          </div>
                        )}
                        itemHeight={72}
                        overscan={5}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Debounce & Throttle Tab */}
          <TabsContent value="debounce" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Debounce & Throttle</CardTitle>
                <CardDescription>
                  Reduce the number of function calls.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <p className="text-muted-foreground">
                  Debounce and throttle are techniques to reduce the number of function calls.
                  This is especially useful for expensive operations like API calls or DOM updates.
                </p>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Debounce Example</h3>
                  <p className="text-sm text-muted-foreground">
                    This search input uses debounce to reduce the number of API calls.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Search className="h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </div>
                    <div className="p-4 border border-border/20 rounded-lg bg-muted/10 min-h-32">
                      {isLoading ? (
                        <div className="flex items-center justify-center h-24">
                          <Loader2 className="h-8 w-8 animate-spin text-primary" />
                        </div>
                      ) : searchResults.length > 0 ? (
                        <ul className="space-y-2">
                          {searchResults.map((result, index) => (
                            <li key={index} className="text-sm">{result}</li>
                          ))}
                        </ul>
                      ) : searchTerm ? (
                        <p className="text-sm text-muted-foreground text-center">No results found</p>
                      ) : (
                        <p className="text-sm text-muted-foreground text-center">Type to search</p>
                      )}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      The search is debounced by 500ms, so the API call is only made after you stop typing.
                    </p>
                  </div>
                </div>
                
                <Separator />
                
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Throttle Example</h3>
                  <p className="text-sm text-muted-foreground">
                    This counter uses throttle to reduce the number of updates.
                  </p>
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <Button onClick={() => setCount(count + 1)}>Increment</Button>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Raw count:</span>
                          <span>{count}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Throttled count:</span>
                          <span>{throttledCount}</span>
                        </div>
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      The throttled count is updated at most once every 300ms, even if the raw count changes more frequently.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}
