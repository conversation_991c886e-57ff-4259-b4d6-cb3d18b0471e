'use client';

/**
 * Accessibility Example Page
 * 
 * This page showcases the accessibility components in action.
 */

import React, { useState } from 'react';
import { PageLayout } from '@/components/layout';
import { 
  SkipLink, 
  VisuallyHidden, 
  FocusTrap, 
  LiveRegion, 
  AccessibleIcon,
  useAnnounce,
  AccessibilityProvider
} from '@/components/ui/accessibility';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { 
  Bell, 
  Info, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Home,
  Settings,
  User,
  Search,
  Accessibility,
  Eye,
  EyeOff,
  Keyboard,
  MousePointer,
  Headphones,
  Volume2,
  VolumeX,
  Zap
} from 'lucide-react';
import { PerformanceProvider } from '@/components/performance/performance-provider';

/**
 * Accessibility Example Page
 */
export default function AccessibilityExamplePage() {
  return (
    <AccessibilityProvider>
      <PerformanceProvider showMonitor={true}>
        <AccessibilityExampleContent />
      </PerformanceProvider>
    </AccessibilityProvider>
  );
}

/**
 * Accessibility Example Content
 */
function AccessibilityExampleContent() {
  const [message, setMessage] = useState('');
  const { announce, Announcer } = useAnnounce();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  
  return (
    <PageLayout
      title="Accessibility Examples"
      description="Examples of accessibility components and best practices"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <SkipLink href="#main-content">Skip to main content</SkipLink>
        
        <Card>
          <CardHeader>
            <CardTitle>Accessibility Components</CardTitle>
            <CardDescription>
              These components help improve accessibility following WCAG 2.1 AA standards.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Accessibility is an important aspect of web development. These components help make your application
              more accessible to users with disabilities, following WCAG 2.1 AA standards.
            </p>
            <div className="flex flex-wrap gap-4 items-center justify-center">
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <Keyboard className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Keyboard</span>
                <span className="text-xs text-muted-foreground">Navigation</span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <Eye className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Visual</span>
                <span className="text-xs text-muted-foreground">Impairments</span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <Headphones className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Screen</span>
                <span className="text-xs text-muted-foreground">Readers</span>
              </div>
              <div className="flex flex-col items-center gap-2">
                <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                  <MousePointer className="h-8 w-8 text-muted-foreground" />
                </div>
                <span className="text-sm font-medium">Motor</span>
                <span className="text-xs text-muted-foreground">Impairments</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <div id="main-content" className="space-y-6">
          <Tabs defaultValue="components" className="w-full">
            <TabsList className="w-full border-b border-border/20 rounded-none justify-start px-4 mb-6">
              <TabsTrigger value="components" className="data-[state=active]:bg-muted/50">
                Components
              </TabsTrigger>
              <TabsTrigger value="forms" className="data-[state=active]:bg-muted/50">
                Accessible Forms
              </TabsTrigger>
              <TabsTrigger value="keyboard" className="data-[state=active]:bg-muted/50">
                Keyboard Navigation
              </TabsTrigger>
              <TabsTrigger value="screen-readers" className="data-[state=active]:bg-muted/50">
                Screen Readers
              </TabsTrigger>
            </TabsList>
            
            {/* Components Tab */}
            <TabsContent value="components" className="space-y-6">
              {/* SkipLink */}
              <Card>
                <CardHeader>
                  <CardTitle>Skip Link</CardTitle>
                  <CardDescription>
                    A skip link that allows keyboard users to bypass navigation and go directly to the main content.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    Skip links are essential for keyboard users to bypass repetitive navigation.
                    They should be the first focusable element on the page and should only be visible when focused.
                  </p>
                  <div className="border border-border/20 rounded-lg p-4">
                    <p className="text-sm text-muted-foreground mb-4">
                      Tab into this container to see the skip link. It will only be visible when focused.
                    </p>
                    <div className="relative border border-border/20 rounded-lg p-4">
                      <SkipLink href="#skip-link-example">Skip to content</SkipLink>
                      <div className="flex flex-col gap-2">
                        <Button variant="outline">Navigation Item 1</Button>
                        <Button variant="outline">Navigation Item 2</Button>
                        <Button variant="outline">Navigation Item 3</Button>
                      </div>
                      <div id="skip-link-example" className="mt-4 p-4 bg-muted/20 rounded-lg">
                        <h3 className="font-medium mb-2">Main Content</h3>
                        <p className="text-sm text-muted-foreground">
                          This is the main content area that users can skip to.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              {/* VisuallyHidden */}
              <Card>
                <CardHeader>
                  <CardTitle>Visually Hidden</CardTitle>
                  <CardDescription>
                    A component that hides content visually but keeps it accessible to screen readers.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    This component is useful for providing additional context to screen reader users without cluttering the visual interface.
                    It uses CSS to hide content visually while keeping it accessible to screen readers.
                  </p>
                  <div className="border border-border/20 rounded-lg p-4">
                    <Button>
                      <Bell className="h-4 w-4 mr-2" />
                      Notifications
                      <VisuallyHidden>(5 unread)</VisuallyHidden>
                    </Button>
                    <p className="text-sm text-muted-foreground mt-4">
                      The text "(5 unread)" is visually hidden but will be announced by screen readers.
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              {/* AccessibleIcon */}
              <Card>
                <CardHeader>
                  <CardTitle>Accessible Icon</CardTitle>
                  <CardDescription>
                    A component that wraps an icon with a visually hidden label for screen readers.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    Icons without text labels need accessible names for screen reader users.
                    This component wraps an icon with a visually hidden label and sets the appropriate ARIA attributes.
                  </p>
                  <div className="border border-border/20 rounded-lg p-4">
                    <div className="flex gap-4">
                      <Button variant="ghost" size="icon">
                        <AccessibleIcon label="Home">
                          <Home className="h-5 w-5" />
                        </AccessibleIcon>
                      </Button>
                      <Button variant="ghost" size="icon">
                        <AccessibleIcon label="Settings">
                          <Settings className="h-5 w-5" />
                        </AccessibleIcon>
                      </Button>
                      <Button variant="ghost" size="icon">
                        <AccessibleIcon label="User Profile">
                          <User className="h-5 w-5" />
                        </AccessibleIcon>
                      </Button>
                      <Button variant="ghost" size="icon">
                        <AccessibleIcon label="Search">
                          <Search className="h-5 w-5" />
                        </AccessibleIcon>
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground mt-4">
                      Each icon has a visually hidden label that will be announced by screen readers.
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              {/* FocusTrap */}
              <Card>
                <CardHeader>
                  <CardTitle>Focus Trap</CardTitle>
                  <CardDescription>
                    A component that traps focus within a container, useful for modals and dialogs.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    Focus trapping is essential for modal dialogs to ensure keyboard users can't accidentally interact with content outside the dialog.
                    This component traps focus within its children and cycles focus back to the first focusable element when the user tabs past the last one.
                  </p>
                  <div className="border border-border/20 rounded-lg p-4">
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                      <DialogTrigger asChild>
                        <Button>Open Dialog with Focus Trap</Button>
                      </DialogTrigger>
                      <DialogContent>
                        <FocusTrap>
                          <DialogHeader>
                            <DialogTitle>Focus is trapped within this dialog</DialogTitle>
                          </DialogHeader>
                          <div className="space-y-4 py-4">
                            <p className="text-sm text-muted-foreground">
                              Try tabbing through the elements in this dialog. Focus will be trapped within it.
                            </p>
                            <div className="flex flex-col gap-2">
                              <Button>Button 1</Button>
                              <Button>Button 2</Button>
                              <Button onClick={() => setIsDialogOpen(false)}>Close Dialog</Button>
                            </div>
                          </div>
                        </FocusTrap>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardContent>
              </Card>
              
              {/* LiveRegion and useAnnounce */}
              <Card>
                <CardHeader>
                  <CardTitle>Live Region & useAnnounce</CardTitle>
                  <CardDescription>
                    Components for announcing dynamic content changes to screen readers.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    Live regions are essential for announcing dynamic content changes to screen readers.
                    Use 'polite' for non-urgent updates and 'assertive' for important alerts that need immediate attention.
                  </p>
                  <div className="border border-border/20 rounded-lg p-4">
                    <div className="space-y-4">
                      <div className="flex flex-wrap gap-2">
                        <Button 
                          variant="outline" 
                          onClick={() => announce('This is a polite announcement', 'polite')}
                        >
                          <Info className="h-4 w-4 mr-2" />
                          Polite Announcement
                        </Button>
                        <Button 
                          variant="outline" 
                          onClick={() => announce('This is an assertive announcement', 'assertive')}
                        >
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          Assertive Announcement
                        </Button>
                      </div>
                      <div className="flex flex-col gap-2">
                        <div className="flex items-center gap-2">
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="h-8 px-2"
                            onClick={() => {
                              setMessage('Success! Your changes have been saved.');
                              announce('Success! Your changes have been saved.', 'polite');
                            }}
                          >
                            <CheckCircle className="h-4 w-4 text-green-500" />
                            <span className="sr-only">Show success message</span>
                          </Button>
                          <Button 
                            size="sm" 
                            variant="ghost" 
                            className="h-8 px-2"
                            onClick={() => {
                              setMessage('Error! Something went wrong.');
                              announce('Error! Something went wrong.', 'assertive');
                            }}
                          >
                            <XCircle className="h-4 w-4 text-red-500" />
                            <span className="sr-only">Show error message</span>
                          </Button>
                          <span className="text-sm">{message}</span>
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        These announcements will be read by screen readers but are not visible on the page.
                      </p>
                    </div>
                    <Announcer />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Forms Tab */}
            <TabsContent value="forms" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Accessible Forms</CardTitle>
                  <CardDescription>
                    Examples of accessible form components.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Text Inputs</h3>
                      <div className="space-y-2">
                        <Label htmlFor="name">Name</Label>
                        <Input id="name" placeholder="Enter your name" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input id="email" type="email" placeholder="Enter your email" />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="password">Password</Label>
                        <Input id="password" type="password" placeholder="Enter your password" />
                        <p className="text-xs text-muted-foreground">
                          Password must be at least 8 characters long.
                        </p>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Checkboxes</h3>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox id="terms" />
                          <Label htmlFor="terms">
                            I agree to the terms and conditions
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="newsletter" />
                          <Label htmlFor="newsletter">
                            Subscribe to newsletter
                          </Label>
                        </div>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Radio Buttons</h3>
                      <RadioGroup defaultValue="option1">
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="option1" id="option1" />
                          <Label htmlFor="option1">Option 1</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="option2" id="option2" />
                          <Label htmlFor="option2">Option 2</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="option3" id="option3" />
                          <Label htmlFor="option3">Option 3</Label>
                        </div>
                      </RadioGroup>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-4">
                      <h3 className="text-lg font-medium">Select</h3>
                      <div className="space-y-2">
                        <Label htmlFor="country">Country</Label>
                        <Select>
                          <SelectTrigger id="country">
                            <SelectValue placeholder="Select a country" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="us">United States</SelectItem>
                            <SelectItem value="ca">Canada</SelectItem>
                            <SelectItem value="uk">United Kingdom</SelectItem>
                            <SelectItem value="au">Australia</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    
                    <Button type="submit">Submit</Button>
                  </form>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Keyboard Navigation Tab */}
            <TabsContent value="keyboard" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Keyboard Navigation</CardTitle>
                  <CardDescription>
                    Examples of keyboard navigation best practices.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-muted-foreground">
                    Keyboard navigation is essential for users who can't use a mouse.
                    All interactive elements should be focusable and operable with a keyboard.
                  </p>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Focus Indicators</h3>
                    <p className="text-sm text-muted-foreground">
                      Focus indicators should be visible and high-contrast. Try tabbing through these buttons.
                    </p>
                    <div className="flex flex-wrap gap-2">
                      <Button>Primary Button</Button>
                      <Button variant="outline">Outline Button</Button>
                      <Button variant="ghost">Ghost Button</Button>
                      <Button variant="link">Link Button</Button>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Logical Tab Order</h3>
                    <p className="text-sm text-muted-foreground">
                      Tab order should follow the visual layout of the page. Try tabbing through these elements.
                    </p>
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      <div className="p-4 border border-border/20 rounded-lg">
                        <h4 className="font-medium mb-2">Step 1</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          This is the first step.
                        </p>
                        <Button>Continue</Button>
                      </div>
                      <div className="p-4 border border-border/20 rounded-lg">
                        <h4 className="font-medium mb-2">Step 2</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          This is the second step.
                        </p>
                        <Button>Continue</Button>
                      </div>
                      <div className="p-4 border border-border/20 rounded-lg">
                        <h4 className="font-medium mb-2">Step 3</h4>
                        <p className="text-sm text-muted-foreground mb-4">
                          This is the third step.
                        </p>
                        <Button>Finish</Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
            
            {/* Screen Readers Tab */}
            <TabsContent value="screen-readers" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Screen Reader Support</CardTitle>
                  <CardDescription>
                    Examples of screen reader best practices.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <p className="text-muted-foreground">
                    Screen readers are essential for users with visual impairments.
                    All content should be accessible to screen readers.
                  </p>
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Alternative Text for Images</h3>
                    <div className="flex flex-col sm:flex-row gap-4">
                      <div className="flex-1 p-4 border border-border/20 rounded-lg">
                        <h4 className="font-medium mb-2">With Alt Text</h4>
                        <div className="w-full h-32 bg-muted/30 rounded-lg flex items-center justify-center">
                          <Accessibility className="h-8 w-8 text-muted-foreground" aria-hidden="true" />
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          This image has alt text: "Accessibility icon"
                        </p>
                      </div>
                      <div className="flex-1 p-4 border border-border/20 rounded-lg">
                        <h4 className="font-medium mb-2">Decorative Image</h4>
                        <div className="w-full h-32 bg-muted/30 rounded-lg flex items-center justify-center">
                          <div className="w-16 h-1 bg-muted-foreground/50 rounded-full" aria-hidden="true" />
                        </div>
                        <p className="text-xs text-muted-foreground mt-2">
                          This decorative image has aria-hidden="true"
                        </p>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">ARIA Attributes</h3>
                    <p className="text-sm text-muted-foreground">
                      ARIA attributes provide additional information to screen readers.
                    </p>
                    <div className="space-y-2">
                      <div className="p-4 border border-border/20 rounded-lg">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">Status:</span>
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100" role="status" aria-live="polite">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Active
                          </span>
                        </div>
                      </div>
                      <div className="p-4 border border-border/20 rounded-lg">
                        <div role="alert" aria-live="assertive" className="p-3 bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100 rounded-lg">
                          <div className="flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4" />
                            <span className="font-medium">Error:</span>
                            <span>This is an error message that will be announced by screen readers.</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </PageLayout>
  );
}
