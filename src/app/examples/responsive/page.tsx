'use client';

/**
 * Responsive Layout Example Page
 * 
 * This page showcases the responsive layout components in action.
 */

import React from 'react';
import { 
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveSection,
  CardGrid,
  PageLayout,
  DashboardLayout
} from '@/components/layout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ResponsiveImage } from '@/components/ui/responsive-image';
import { 
  LayoutGrid, 
  Layers, 
  Grid3X3, 
  LayoutDashboard, 
  LayoutList,
  Maximize,
  Minimize,
  Smartphone,
  Tablet,
  Monitor,
  ArrowRight
} from 'lucide-react';
import { PerformanceProvider } from '@/components/performance/performance-provider';
import { AccessibilityProvider } from '@/components/ui/accessibility';

/**
 * Responsive Layout Example Page
 */
export default function ResponsiveLayoutExamplePage() {
  return (
    <AccessibilityProvider>
      <PerformanceProvider showMonitor={true}>
        <ResponsiveLayoutExample />
      </PerformanceProvider>
    </AccessibilityProvider>
  );
}

/**
 * Responsive Layout Example Content
 */
function ResponsiveLayoutExample() {
  // Example data for components
  const cardItems = [
    {
      id: 'card1',
      title: 'Responsive Container',
      description: 'A container with consistent sizing and spacing',
      content: (
        <div className="h-32 flex items-center justify-center">
          <Maximize className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button variant="outline" size="sm" className="w-full">
          Learn More
        </Button>
      ),
    },
    {
      id: 'card2',
      title: 'Responsive Grid',
      description: 'A grid layout that adapts to different screen sizes',
      content: (
        <div className="h-32 flex items-center justify-center">
          <Grid3X3 className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button variant="outline" size="sm" className="w-full">
          Learn More
        </Button>
      ),
    },
    {
      id: 'card3',
      title: 'Responsive Section',
      description: 'A section with consistent spacing and layout',
      content: (
        <div className="h-32 flex items-center justify-center">
          <Layers className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button variant="outline" size="sm" className="w-full">
          Learn More
        </Button>
      ),
    },
    {
      id: 'card4',
      title: 'Card Grid',
      description: 'A grid of cards that adapts to different screen sizes',
      content: (
        <div className="h-32 flex items-center justify-center">
          <LayoutGrid className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button variant="outline" size="sm" className="w-full">
          Learn More
        </Button>
      ),
    },
    {
      id: 'card5',
      title: 'Page Layout',
      description: 'A layout for pages with consistent spacing',
      content: (
        <div className="h-32 flex items-center justify-center">
          <LayoutList className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button variant="outline" size="sm" className="w-full">
          Learn More
        </Button>
      ),
    },
    {
      id: 'card6',
      title: 'Dashboard Layout',
      description: 'A layout for dashboards with consistent spacing',
      content: (
        <div className="h-32 flex items-center justify-center">
          <LayoutDashboard className="h-8 w-8 text-muted-foreground" />
        </div>
      ),
      footer: (
        <Button variant="outline" size="sm" className="w-full">
          Learn More
        </Button>
      ),
    },
  ];
  
  return (
    <PageLayout
      title="Responsive Layout Examples"
      description="Examples of responsive layout components"
      maxWidth="xl"
      padding="md"
    >
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="w-full border-b border-border/20 rounded-none justify-start px-4 mb-6">
          <TabsTrigger value="overview" className="data-[state=active]:bg-muted/50">
            Overview
          </TabsTrigger>
          <TabsTrigger value="container" className="data-[state=active]:bg-muted/50">
            Container
          </TabsTrigger>
          <TabsTrigger value="grid" className="data-[state=active]:bg-muted/50">
            Grid
          </TabsTrigger>
          <TabsTrigger value="cards" className="data-[state=active]:bg-muted/50">
            Cards
          </TabsTrigger>
          <TabsTrigger value="layouts" className="data-[state=active]:bg-muted/50">
            Layouts
          </TabsTrigger>
        </TabsList>
        
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Responsive Layout Components</CardTitle>
              <CardDescription>
                These components are designed to create responsive layouts that work well on all screen sizes.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  The responsive layout components are designed to help you create layouts that adapt to different screen sizes.
                  They follow iOS 19 design principles with clean lines, subtle shadows, and rounded corners.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                      <Smartphone className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <span className="text-sm font-medium">Mobile</span>
                    <span className="text-xs text-muted-foreground">375px</span>
                  </div>
                  <ArrowRight className="hidden sm:block h-4 w-4 text-muted-foreground" />
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                      <Tablet className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <span className="text-sm font-medium">Tablet</span>
                    <span className="text-xs text-muted-foreground">768px</span>
                  </div>
                  <ArrowRight className="hidden sm:block h-4 w-4 text-muted-foreground" />
                  <div className="flex flex-col items-center gap-2">
                    <div className="w-16 h-16 rounded-full bg-muted/30 flex items-center justify-center">
                      <Monitor className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <span className="text-sm font-medium">Desktop</span>
                    <span className="text-xs text-muted-foreground">1280px+</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <CardGrid
            items={cardItems}
            mobileColumns={1}
            tabletColumns={2}
            desktopColumns={3}
            largeDesktopColumns={3}
            gap="md"
            glass={true}
            bordered={true}
            shadowed={true}
            rounded={true}
            equalHeight={true}
          />
        </TabsContent>
        
        {/* Container Tab */}
        <TabsContent value="container" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Responsive Container</CardTitle>
              <CardDescription>
                A responsive container that provides consistent sizing and spacing.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-muted-foreground">
                The ResponsiveContainer component provides a consistent container for content with responsive sizing.
                It adapts to different screen sizes and provides consistent padding and spacing.
              </p>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Default Container</h3>
                <div className="border border-border/20 rounded-lg p-4 bg-muted/10">
                  <ResponsiveContainer className="border border-border/20 rounded-lg p-4 bg-card">
                    <div className="h-32 flex items-center justify-center">
                      <p className="text-center text-muted-foreground">
                        This is a responsive container with default settings.
                      </p>
                    </div>
                  </ResponsiveContainer>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Container with Glass Effect</h3>
                <div className="border border-border/20 rounded-lg p-4 bg-muted/10">
                  <ResponsiveContainer
                    glass={true}
                    bordered={true}
                    shadowed={true}
                    rounded={true}
                    className="p-4"
                  >
                    <div className="h-32 flex items-center justify-center">
                      <p className="text-center text-muted-foreground">
                        This is a responsive container with glass effect.
                      </p>
                    </div>
                  </ResponsiveContainer>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Container with Different Max Widths</h3>
                <div className="border border-border/20 rounded-lg p-4 bg-muted/10 space-y-4">
                  <ResponsiveContainer
                    maxWidth="sm"
                    bordered={true}
                    className="p-4 bg-card"
                  >
                    <p className="text-center text-muted-foreground">
                      Small Container (sm)
                    </p>
                  </ResponsiveContainer>
                  
                  <ResponsiveContainer
                    maxWidth="md"
                    bordered={true}
                    className="p-4 bg-card"
                  >
                    <p className="text-center text-muted-foreground">
                      Medium Container (md)
                    </p>
                  </ResponsiveContainer>
                  
                  <ResponsiveContainer
                    maxWidth="lg"
                    bordered={true}
                    className="p-4 bg-card"
                  >
                    <p className="text-center text-muted-foreground">
                      Large Container (lg)
                    </p>
                  </ResponsiveContainer>
                  
                  <ResponsiveContainer
                    maxWidth="xl"
                    bordered={true}
                    className="p-4 bg-card"
                  >
                    <p className="text-center text-muted-foreground">
                      Extra Large Container (xl)
                    </p>
                  </ResponsiveContainer>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Grid Tab */}
        <TabsContent value="grid" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Responsive Grid</CardTitle>
              <CardDescription>
                A responsive grid layout that adapts to different screen sizes.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-muted-foreground">
                The ResponsiveGrid component provides a responsive grid layout that adapts to different screen sizes.
                It uses CSS Grid to create a flexible and responsive layout.
              </p>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Default Grid</h3>
                <div className="border border-border/20 rounded-lg p-4 bg-muted/10">
                  <ResponsiveGrid
                    mobileColumns={1}
                    tabletColumns={2}
                    desktopColumns={3}
                    largeDesktopColumns={4}
                    gap="md"
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                      <div
                        key={item}
                        className="bg-card border border-border/20 rounded-lg p-4 h-24 flex items-center justify-center"
                      >
                        Item {item}
                      </div>
                    ))}
                  </ResponsiveGrid>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Auto-Fit Grid</h3>
                <div className="border border-border/20 rounded-lg p-4 bg-muted/10">
                  <ResponsiveGrid
                    autoFit={true}
                    minItemWidth="150px"
                    gap="md"
                  >
                    {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                      <div
                        key={item}
                        className="bg-card border border-border/20 rounded-lg p-4 h-24 flex items-center justify-center"
                      >
                        Item {item}
                      </div>
                    ))}
                  </ResponsiveGrid>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Grid with Different Gaps</h3>
                <div className="border border-border/20 rounded-lg p-4 bg-muted/10 space-y-4">
                  <p className="text-sm text-muted-foreground">Small Gap (sm)</p>
                  <ResponsiveGrid
                    mobileColumns={2}
                    tabletColumns={3}
                    desktopColumns={4}
                    gap="sm"
                  >
                    {[1, 2, 3, 4].map((item) => (
                      <div
                        key={item}
                        className="bg-card border border-border/20 rounded-lg p-4 h-16 flex items-center justify-center"
                      >
                        {item}
                      </div>
                    ))}
                  </ResponsiveGrid>
                  
                  <p className="text-sm text-muted-foreground mt-4">Medium Gap (md)</p>
                  <ResponsiveGrid
                    mobileColumns={2}
                    tabletColumns={3}
                    desktopColumns={4}
                    gap="md"
                  >
                    {[1, 2, 3, 4].map((item) => (
                      <div
                        key={item}
                        className="bg-card border border-border/20 rounded-lg p-4 h-16 flex items-center justify-center"
                      >
                        {item}
                      </div>
                    ))}
                  </ResponsiveGrid>
                  
                  <p className="text-sm text-muted-foreground mt-4">Large Gap (lg)</p>
                  <ResponsiveGrid
                    mobileColumns={2}
                    tabletColumns={3}
                    desktopColumns={4}
                    gap="lg"
                  >
                    {[1, 2, 3, 4].map((item) => (
                      <div
                        key={item}
                        className="bg-card border border-border/20 rounded-lg p-4 h-16 flex items-center justify-center"
                      >
                        {item}
                      </div>
                    ))}
                  </ResponsiveGrid>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Cards Tab */}
        <TabsContent value="cards" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Card Grid</CardTitle>
              <CardDescription>
                A responsive grid of cards that adapts to different screen sizes.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <p className="text-muted-foreground">
                The CardGrid component combines the ResponsiveGrid with Card components to create a responsive grid of cards.
                It follows iOS 19 design principles with clean lines, subtle shadows, and rounded corners.
              </p>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Default Card Grid</h3>
                <CardGrid
                  items={cardItems.slice(0, 4)}
                  mobileColumns={1}
                  tabletColumns={2}
                  desktopColumns={2}
                  largeDesktopColumns={4}
                  gap="md"
                  glass={true}
                  bordered={true}
                  shadowed={true}
                  rounded={true}
                  equalHeight={true}
                />
              </div>
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Card Grid with Different Styles</h3>
                <CardGrid
                  items={cardItems.slice(0, 3)}
                  mobileColumns={1}
                  tabletColumns={3}
                  desktopColumns={3}
                  gap="md"
                  glass={false}
                  bordered={true}
                  shadowed={false}
                  rounded={true}
                  equalHeight={true}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Layouts Tab */}
        <TabsContent value="layouts" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Page Layout</CardTitle>
              <CardDescription>
                A layout for pages with consistent spacing.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border border-border/20 rounded-lg p-4 bg-muted/10">
                <div className="border border-border/20 rounded-lg overflow-hidden">
                  <div className="bg-card p-4 border-b border-border/20">
                    <h3 className="text-lg font-medium">Page Title</h3>
                    <p className="text-sm text-muted-foreground">Page description</p>
                  </div>
                  <div className="p-4">
                    <div className="h-32 flex items-center justify-center bg-muted/20 rounded-lg">
                      <p className="text-center text-muted-foreground">
                        Page content goes here
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Dashboard Layout</CardTitle>
              <CardDescription>
                A layout for dashboards with consistent spacing.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="border border-border/20 rounded-lg p-4 bg-muted/10">
                <div className="border border-border/20 rounded-lg overflow-hidden">
                  <div className="bg-card p-4 border-b border-border/20">
                    <h3 className="text-lg font-medium">Dashboard Title</h3>
                    <p className="text-sm text-muted-foreground">Dashboard description</p>
                  </div>
                  <div className="p-4">
                    <ResponsiveGrid
                      mobileColumns={1}
                      tabletColumns={2}
                      desktopColumns={3}
                      gap="md"
                    >
                      {[1, 2, 3, 4, 5, 6].map((item) => (
                        <div
                          key={item}
                          className="bg-card border border-border/20 rounded-lg p-4 h-24 flex items-center justify-center"
                        >
                          Widget {item}
                        </div>
                      ))}
                    </ResponsiveGrid>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
}
