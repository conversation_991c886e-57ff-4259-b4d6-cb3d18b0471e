'use client';

/**
 * Mobile Components Example Page
 * 
 * This page showcases the mobile-friendly components in action.
 */

import React, { useState } from 'react';
import { ResponsiveLayoutWrapper } from '@/components/layout';
import { 
  SwipeContainer,
  PullToRefresh,
  MobileTabs,
  MobileAccordion,
  BottomSheet,
  ActionSheet,
  MobileGallery,
  MobileToastProvider,
  useToast
} from '@/components/ui/mobile-ui';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Home, 
  Settings, 
  User, 
  Bell, 
  MessageSquare,
  Trash,
  Share,
  Edit,
  Copy,
  Download,
  Info,
  ChevronRight,
  RefreshCw,
  Check,
  X
} from 'lucide-react';
import { PerformanceProvider, usePerformance } from '@/components/performance/performance-provider';
import { PerformanceMonitor } from '@/components/performance/performance-monitor';
import { AccessibilityProvider } from '@/components/ui/accessibility';

/**
 * Mobile Components Example Page
 */
export default function MobileExamplePage() {
  return (
    <AccessibilityProvider>
      <PerformanceProvider showMonitor={true}>
        <MobileToastProvider position="bottom-center">
          <MobileExampleContent />
        </MobileToastProvider>
      </PerformanceProvider>
    </AccessibilityProvider>
  );
}

/**
 * Mobile Example Content
 */
function MobileExampleContent() {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [isActionSheetOpen, setIsActionSheetOpen] = useState(false);
  const { showToast } = useToast();
  const performance = usePerformance();
  
  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    // Simulate a network request
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsRefreshing(false);
    showToast({
      type: 'success',
      title: 'Refreshed',
      message: 'Content has been refreshed',
      duration: 3000,
    });
  };
  
  // Example data for components
  const tabItems = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Home Tab</h3>
          <p className="text-muted-foreground mb-4">This is the home tab content.</p>
          <Card>
            <CardHeader>
              <CardTitle>Welcome</CardTitle>
              <CardDescription>This is a mobile-friendly card component.</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                This example page showcases the mobile-friendly components in action.
              </p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm">Learn More</Button>
            </CardFooter>
          </Card>
        </div>
      ),
    },
    {
      id: 'messages',
      label: 'Messages',
      icon: <MessageSquare className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Messages Tab</h3>
          <p className="text-muted-foreground mb-4">This is the messages tab content.</p>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 bg-muted/20 rounded-lg">
              <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                <User className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <h4 className="font-medium">John Doe</h4>
                  <span className="text-xs text-muted-foreground">10:30 AM</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Hey, how are you doing today?
                </p>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-muted/20 rounded-lg">
              <div className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center">
                <User className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <h4 className="font-medium">Jane Smith</h4>
                  <span className="text-xs text-muted-foreground">Yesterday</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Can you send me the files we discussed?
                </p>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: <Bell className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Notifications Tab</h3>
          <p className="text-muted-foreground mb-4">This is the notifications tab content.</p>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 bg-muted/20 rounded-lg">
              <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center">
                <Info className="h-5 w-5 text-blue-500" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">System Update</h4>
                <p className="text-sm text-muted-foreground">
                  A new update is available for your app.
                </p>
                <div className="flex gap-2 mt-2">
                  <Button size="sm" variant="outline">Later</Button>
                  <Button size="sm">Update Now</Button>
                </div>
              </div>
            </div>
            <div className="flex items-start gap-3 p-3 bg-muted/20 rounded-lg">
              <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center">
                <Check className="h-5 w-5 text-green-500" />
              </div>
              <div className="flex-1">
                <h4 className="font-medium">Task Completed</h4>
                <p className="text-sm text-muted-foreground">
                  You have completed all your tasks for today.
                </p>
              </div>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Profile Tab</h3>
          <p className="text-muted-foreground mb-4">This is the profile tab content.</p>
          <div className="flex flex-col items-center gap-4">
            <div className="w-20 h-20 rounded-full bg-primary/20 flex items-center justify-center">
              <User className="h-10 w-10 text-primary" />
            </div>
            <div className="text-center">
              <h4 className="font-medium text-lg">John Doe</h4>
              <p className="text-sm text-muted-foreground"><EMAIL></p>
            </div>
            <div className="w-full space-y-4 mt-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input id="name" defaultValue="John Doe" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" defaultValue="<EMAIL>" />
              </div>
              <Button className="w-full">Save Changes</Button>
            </div>
          </div>
        </div>
      ),
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Settings Tab</h3>
          <p className="text-muted-foreground mb-4">This is the settings tab content.</p>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-muted/50 flex items-center justify-center">
                  <Bell className="h-4 w-4" />
                </div>
                <span>Notifications</span>
              </div>
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-muted/50 flex items-center justify-center">
                  <User className="h-4 w-4" />
                </div>
                <span>Account</span>
              </div>
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-muted/50 flex items-center justify-center">
                  <Settings className="h-4 w-4" />
                </div>
                <span>Preferences</span>
              </div>
              <ChevronRight className="h-4 w-4 text-muted-foreground" />
            </div>
          </div>
        </div>
      ),
    },
  ];
  
  const accordionItems = [
    {
      id: 'item1',
      title: 'What are mobile-friendly components?',
      content: (
        <div>
          <p className="text-muted-foreground">
            Mobile-friendly components are designed to provide a better user experience on mobile devices.
            They are optimized for touch interactions, smaller screens, and mobile-specific gestures.
          </p>
        </div>
      ),
    },
    {
      id: 'item2',
      title: 'How do I use these components?',
      content: (
        <div>
          <p className="text-muted-foreground">
            You can import these components from the <code>@/components/ui/mobile-ui</code> module
            and use them in your React components. Check the documentation for more details.
          </p>
        </div>
      ),
    },
    {
      id: 'item3',
      title: 'Are these components accessible?',
      content: (
        <div>
          <p className="text-muted-foreground">
            Yes, these components are designed with accessibility in mind. They follow WCAG 2.1 AA
            standards and provide proper ARIA attributes and keyboard navigation.
          </p>
        </div>
      ),
    },
  ];
  
  const actionSheetItems = [
    {
      id: 'edit',
      label: 'Edit',
      icon: <Edit className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Edit clicked' }),
    },
    {
      id: 'share',
      label: 'Share',
      icon: <Share className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Share clicked' }),
    },
    {
      id: 'copy',
      label: 'Copy',
      icon: <Copy className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Copy clicked' }),
    },
    {
      id: 'download',
      label: 'Download',
      icon: <Download className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Download clicked' }),
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <Trash className="h-4 w-4" />,
      destructive: true,
      onClick: () => showToast({ type: 'error', message: 'Delete clicked' }),
    },
  ];
  
  const galleryImages = [
    {
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
      alt: 'Mountain landscape',
    },
    {
      src: 'https://images.unsplash.com/photo-1454496522488-7a8e488e8606',
      alt: 'Mountain range',
    },
    {
      src: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
      alt: 'Mountain view',
    },
  ];
  
  return (
    <ResponsiveLayoutWrapper
      title="Mobile Components"
      description="Example of mobile-friendly components"
      showTopNav={true}
      showBottomNav={true}
      navItems={[
        {
          href: '#',
          label: 'Home',
          icon: <Home className="h-4 w-4" />,
        },
        {
          href: '#',
          label: 'Messages',
          icon: <MessageSquare className="h-4 w-4" />,
        },
        {
          href: '#',
          label: 'Notifications',
          icon: <Bell className="h-4 w-4" />,
        },
        {
          href: '#',
          label: 'Profile',
          icon: <User className="h-4 w-4" />,
        },
        {
          href: '#',
          label: 'Settings',
          icon: <Settings className="h-4 w-4" />,
        },
      ]}
    >
      <PullToRefresh
        onRefresh={handleRefresh}
        refreshingText="Refreshing..."
        pullText="Pull to refresh"
        releaseText="Release to refresh"
      >
        <div className="space-y-6 pb-20">
          {/* Device Info */}
          <Card>
            <CardHeader>
              <CardTitle>Device Information</CardTitle>
              <CardDescription>Information about your device</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Device Type:</span>
                  <span>
                    {performance.isMobile ? 'Mobile' : performance.isTablet ? 'Tablet' : 'Desktop'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Orientation:</span>
                  <span>{performance.isPortrait ? 'Portrait' : 'Landscape'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Touch Screen:</span>
                  <span>{performance.hasTouch ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Connection:</span>
                  <span>
                    {performance.isOnline ? 'Online' : 'Offline'}
                    {performance.isDataSaver && ' (Data Saver)'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Reduced Motion:</span>
                  <span>{performance.hasReducedMotion ? 'Yes' : 'No'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Dark Mode:</span>
                  <span>{performance.isDarkMode ? 'Yes' : 'No'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Tabs Example */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Tabs</h2>
            <p className="text-sm text-muted-foreground">
              A mobile-friendly tab interface with swipe navigation.
            </p>
            <MobileTabs
              tabs={tabItems}
              defaultTab="home"
              enableSwipe={true}
              showArrows={true}
              showPagination={true}
            />
          </div>
          
          {/* Accordion Example */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Accordion</h2>
            <p className="text-sm text-muted-foreground">
              A mobile-friendly accordion with touch-friendly interactions.
            </p>
            <MobileAccordion
              items={accordionItems}
              defaultOpen={['item1']}
              allowMultiple={true}
              collapseOthers={false}
              bordered={true}
              glass={true}
            />
          </div>
          
          {/* Gallery Example */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Gallery</h2>
            <p className="text-sm text-muted-foreground">
              A mobile-friendly image gallery with swipe navigation.
            </p>
            <div className="h-64">
              <MobileGallery
                images={galleryImages}
                initialIndex={0}
                enableSwipe={true}
                showControls={true}
                showPagination={true}
                enableZoom={true}
                enableFullscreen={true}
                glass={true}
                bordered={true}
                shadowed={true}
                rounded={true}
              />
            </div>
          </div>
          
          {/* Swipe Container Example */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Swipe Container</h2>
            <p className="text-sm text-muted-foreground">
              A container that provides swipe gesture functionality.
            </p>
            <SwipeContainer
              className="h-32 bg-muted/30 rounded-lg flex items-center justify-center"
              onSwipeLeft={() => showToast({ type: 'info', message: 'Swiped left' })}
              onSwipeRight={() => showToast({ type: 'info', message: 'Swiped right' })}
              onSwipeUp={() => showToast({ type: 'info', message: 'Swiped up' })}
              onSwipeDown={() => showToast({ type: 'info', message: 'Swiped down' })}
            >
              <div className="text-center">
                <p className="font-medium">Swipe me</p>
                <p className="text-sm text-muted-foreground">Try swiping in any direction</p>
              </div>
            </SwipeContainer>
          </div>
          
          {/* Bottom Sheet and Action Sheet Example */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Bottom Sheets</h2>
            <p className="text-sm text-muted-foreground">
              Mobile-friendly bottom sheets and action sheets.
            </p>
            <div className="flex flex-wrap gap-2">
              <Button onClick={() => setIsBottomSheetOpen(true)}>
                Open Bottom Sheet
              </Button>
              <Button onClick={() => setIsActionSheetOpen(true)}>
                Open Action Sheet
              </Button>
            </div>
          </div>
          
          {/* Toast Example */}
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">Toast Notifications</h2>
            <p className="text-sm text-muted-foreground">
              Mobile-friendly toast notifications.
            </p>
            <div className="flex flex-wrap gap-2">
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'success', 
                    title: 'Success', 
                    message: 'This is a success toast notification',
                    duration: 3000,
                  })
                }
              >
                Success Toast
              </Button>
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'error', 
                    title: 'Error', 
                    message: 'This is an error toast notification',
                    duration: 3000,
                  })
                }
              >
                Error Toast
              </Button>
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'info', 
                    title: 'Info', 
                    message: 'This is an info toast notification',
                    duration: 3000,
                  })
                }
              >
                Info Toast
              </Button>
              <Button 
                onClick={() => 
                  showToast({ 
                    type: 'warning', 
                    title: 'Warning', 
                    message: 'This is a warning toast notification',
                    duration: 3000,
                  })
                }
              >
                Warning Toast
              </Button>
            </div>
          </div>
        </div>
      </PullToRefresh>
      
      {/* Bottom Sheet */}
      <BottomSheet
        isOpen={isBottomSheetOpen}
        onClose={() => setIsBottomSheetOpen(false)}
        title="Bottom Sheet"
        showCloseButton={true}
        showDragHandle={true}
      >
        <div className="p-4 space-y-4">
          <p className="text-muted-foreground">
            This is a bottom sheet component that follows iOS 19 design principles.
            It provides a mobile-friendly way to display content that slides up from the bottom of the screen.
          </p>
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input id="name" placeholder="Enter your name" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" placeholder="Enter your email" />
          </div>
          <Button onClick={() => setIsBottomSheetOpen(false)} className="w-full">
            Close
          </Button>
        </div>
      </BottomSheet>
      
      {/* Action Sheet */}
      <ActionSheet
        isOpen={isActionSheetOpen}
        onClose={() => setIsActionSheetOpen(false)}
        title="Action Sheet"
        items={actionSheetItems}
        showCancel={true}
        cancelLabel="Cancel"
      />
    </ResponsiveLayoutWrapper>
  );
}
