'use client';

/**
 * Examples Index Page
 * 
 * This page provides an index of all example pages.
 */

import React from 'react';
import { PageLayout } from '@/components/layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { 
  Smartphone, 
  Layout, 
  Zap, 
  Accessibility, 
  ChevronRight,
  Palette,
  Component,
  Layers,
  FileCode,
  BookOpen
} from 'lucide-react';
import { PerformanceProvider } from '@/components/performance/performance-provider';
import { AccessibilityProvider } from '@/components/ui/accessibility';

/**
 * Examples Index Page
 */
export default function ExamplesIndexPage() {
  return (
    <AccessibilityProvider>
      <PerformanceProvider showMonitor={true}>
        <ExamplesIndexContent />
      </PerformanceProvider>
    </AccessibilityProvider>
  );
}

/**
 * Examples Index Content
 */
function ExamplesIndexContent() {
  const exampleCategories = [
    {
      title: 'Mobile Components',
      description: 'Examples of mobile-friendly components following iOS 19 design principles',
      icon: <Smartphone className="h-6 w-6" />,
      href: '/examples/mobile',
      color: 'bg-blue-500/10 text-blue-500',
    },
    {
      title: 'Responsive Layout',
      description: 'Examples of responsive layout components that work on all screen sizes',
      icon: <Layout className="h-6 w-6" />,
      href: '/examples/responsive',
      color: 'bg-purple-500/10 text-purple-500',
    },
    {
      title: 'Performance Optimization',
      description: 'Examples of performance optimization components and techniques',
      icon: <Zap className="h-6 w-6" />,
      href: '/examples/performance',
      color: 'bg-amber-500/10 text-amber-500',
    },
    {
      title: 'Accessibility',
      description: 'Examples of accessibility components and best practices',
      icon: <Accessibility className="h-6 w-6" />,
      href: '/examples/accessibility',
      color: 'bg-green-500/10 text-green-500',
    },
  ];
  
  return (
    <PageLayout
      title="Examples"
      description="Examples of components and features"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold">Examples</h1>
          <p className="text-xl text-muted-foreground">
            Explore examples of components and features that showcase the capabilities of the application.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {exampleCategories.map((category) => (
            <Card key={category.title} className="overflow-hidden">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${category.color}`}>
                    {category.icon}
                  </div>
                  <CardTitle>{category.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  {category.description}
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" className="w-full justify-between">
                  <Link href={category.href}>
                    <span>View Example</span>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>About These Examples</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              These examples showcase the components and features that have been implemented to improve the mobile experience,
              accessibility, and performance of the application. They demonstrate how to use the components and how they adapt
              to different screen sizes and device capabilities.
            </p>
            <div className="mt-4 space-y-2">
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-500/10 flex items-center justify-center mt-0.5">
                  <Smartphone className="h-3 w-3 text-blue-500" />
                </div>
                <div>
                  <p className="font-medium">Mobile Components</p>
                  <p className="text-sm text-muted-foreground">
                    Mobile-friendly components with touch interactions, swipe gestures, and responsive layouts.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-purple-500/10 flex items-center justify-center mt-0.5">
                  <Layout className="h-3 w-3 text-purple-500" />
                </div>
                <div>
                  <p className="font-medium">Responsive Layout</p>
                  <p className="text-sm text-muted-foreground">
                    Layout components that adapt to different screen sizes and device capabilities.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-amber-500/10 flex items-center justify-center mt-0.5">
                  <Zap className="h-3 w-3 text-amber-500" />
                </div>
                <div>
                  <p className="font-medium">Performance Optimization</p>
                  <p className="text-sm text-muted-foreground">
                    Techniques to improve performance, especially on mobile devices with limited resources.
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-2">
                <div className="w-6 h-6 rounded-full bg-green-500/10 flex items-center justify-center mt-0.5">
                  <Accessibility className="h-3 w-3 text-green-500" />
                </div>
                <div>
                  <p className="font-medium">Accessibility</p>
                  <p className="text-sm text-muted-foreground">
                    Components and practices to improve accessibility for users with disabilities.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link href="/docs">
                <span>View Documentation</span>
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </PageLayout>
  );
}
