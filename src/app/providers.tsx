'use client';

import React, { ReactNode, useEffect } from 'react';

// Import from feature folders
import { WeatherProvider } from '@/features/weather/context';
import { NotificationProvider, SimpleNotificationProvider } from '@/features/notifications/context';
import { SafeDataProvider } from '@/features/data-validation/context';

// Import from components
import { ErrorBoundary } from '@/components/common';
import { ThemeProvider } from '@/components/providers/theme-provider';
import { KeyboardFocusOutline } from '@/components/ui/KeyboardFocusOutline';

// Import our new providers
import { PerformanceProvider } from '@/components/performance/performance-provider';
import { AccessibilityProvider } from '@/components/ui/accessibility';
import { MobileToastProvider } from '@/components/ui/mobile-ui';
import { AnalyticsProvider } from '@/components/analytics/analytics-provider';

// Legacy providers
import { ToastProvider } from '@/components/ui/toast';

// Import from lib
import { initSentry } from '@/lib/sentry';

interface ProvidersProps {
  children: ReactNode;
}

export default function Providers({ children }: ProvidersProps) {
  // Initialize Sentry on the client side
  useEffect(() => {
    initSentry();
  }, []);

  return (
    <ErrorBoundary>
      <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
        {/* Analytics provider */}
        <AnalyticsProvider
          enabled={true}
          debug={process.env.NODE_ENV === 'development'}
          trackPageViews={true}
          trackErrors={true}
          trackPerformance={true}
          trackUserInteractions={true}
        >
          {/* Performance provider with monitoring */}
          <PerformanceProvider showMonitor={process.env.NODE_ENV === 'development'} monitorDevOnly={true}>
            {/* Accessibility provider */}
            <AccessibilityProvider>
              <KeyboardFocusOutline />
              {/* Mobile toast provider */}
              <MobileToastProvider position="bottom-center">
                {/* Legacy toast provider for backward compatibility */}
                <ToastProvider>
                  <NotificationProvider>
                    <SimpleNotificationProvider>
                      <SafeDataProvider>
                        <WeatherProvider>{children}</WeatherProvider>
                      </SafeDataProvider>
                    </SimpleNotificationProvider>
                  </NotificationProvider>
                </ToastProvider>
              </MobileToastProvider>
            </AccessibilityProvider>
          </PerformanceProvider>
        </AnalyticsProvider>
      </ThemeProvider>
    </ErrorBoundary>
  );
}
