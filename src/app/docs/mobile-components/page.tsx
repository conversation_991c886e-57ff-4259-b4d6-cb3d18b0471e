'use client';

/**
 * Mobile Components Documentation Page
 * 
 * This page provides documentation for mobile-friendly components.
 */

import React from 'react';
import { ComponentDoc } from '@/components/docs/ComponentDoc';
import { PageLayout } from '@/components/layout';
import { SwipeContainer } from '@/components/ui/swipe-container';
import { PullToRefresh } from '@/components/ui/pull-to-refresh';
import { MobileTabs } from '@/components/ui/mobile-tabs';
import { MobileAccordion } from '@/components/ui/mobile-accordion';
import { BottomSheet } from '@/components/ui/bottom-sheet';
import { ActionSheet } from '@/components/ui/action-sheet';
import { MobileGallery } from '@/components/ui/mobile-gallery';
import { MobileToastProvider, useToast } from '@/components/ui/mobile-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Home, 
  Settings, 
  User, 
  Bell, 
  MessageSquare,
  Trash,
  Share,
  Edit,
  Copy,
  Download,
  Info
} from 'lucide-react';
import { useState } from 'react';

/**
 * Mobile Components Documentation Page
 */
export default function MobileComponentsPage() {
  const [isBottomSheetOpen, setIsBottomSheetOpen] = useState(false);
  const [isActionSheetOpen, setIsActionSheetOpen] = useState(false);
  const { showToast } = useToast();
  
  // Example data for components
  const tabItems = [
    {
      id: 'home',
      label: 'Home',
      icon: <Home className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Home Tab</h3>
          <p className="text-muted-foreground">This is the home tab content.</p>
        </div>
      ),
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Settings Tab</h3>
          <p className="text-muted-foreground">This is the settings tab content.</p>
        </div>
      ),
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: <User className="h-4 w-4" />,
      content: (
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-2">Profile Tab</h3>
          <p className="text-muted-foreground">This is the profile tab content.</p>
        </div>
      ),
    },
  ];
  
  const accordionItems = [
    {
      id: 'item1',
      title: 'Accordion Item 1',
      content: (
        <div>
          <p className="text-muted-foreground">This is the content for accordion item 1.</p>
        </div>
      ),
    },
    {
      id: 'item2',
      title: 'Accordion Item 2',
      content: (
        <div>
          <p className="text-muted-foreground">This is the content for accordion item 2.</p>
        </div>
      ),
    },
    {
      id: 'item3',
      title: 'Accordion Item 3',
      content: (
        <div>
          <p className="text-muted-foreground">This is the content for accordion item 3.</p>
        </div>
      ),
    },
  ];
  
  const actionSheetItems = [
    {
      id: 'edit',
      label: 'Edit',
      icon: <Edit className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Edit clicked' }),
    },
    {
      id: 'share',
      label: 'Share',
      icon: <Share className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Share clicked' }),
    },
    {
      id: 'copy',
      label: 'Copy',
      icon: <Copy className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Copy clicked' }),
    },
    {
      id: 'download',
      label: 'Download',
      icon: <Download className="h-4 w-4" />,
      onClick: () => showToast({ type: 'info', message: 'Download clicked' }),
    },
    {
      id: 'delete',
      label: 'Delete',
      icon: <Trash className="h-4 w-4" />,
      destructive: true,
      onClick: () => showToast({ type: 'error', message: 'Delete clicked' }),
    },
  ];
  
  const galleryImages = [
    {
      src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4',
      alt: 'Mountain landscape',
    },
    {
      src: 'https://images.unsplash.com/photo-1454496522488-7a8e488e8606',
      alt: 'Mountain range',
    },
    {
      src: 'https://images.unsplash.com/photo-1464822759023-fed622ff2c3b',
      alt: 'Mountain view',
    },
  ];
  
  return (
    <PageLayout
      title="Mobile Components"
      description="Documentation for mobile-friendly components"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <h1 className="text-3xl font-bold">Mobile Components</h1>
        <p className="text-lg text-muted-foreground">
          These components are designed to provide a mobile-friendly experience following iOS 19 design principles.
        </p>
        
        <div className="grid grid-cols-1 gap-8">
          {/* SwipeContainer */}
          <ComponentDoc
            name="SwipeContainer"
            description="A container that provides swipe gesture functionality for mobile interactions."
            example={
              <SwipeContainer
                className="w-full max-w-xs h-32 bg-muted/30 rounded-lg flex items-center justify-center"
                onSwipeLeft={() => showToast({ type: 'info', message: 'Swiped left' })}
                onSwipeRight={() => showToast({ type: 'info', message: 'Swiped right' })}
              >
                <div className="text-center">
                  <p className="font-medium">Swipe me</p>
                  <p className="text-sm text-muted-foreground">Try swiping left or right</p>
                </div>
              </SwipeContainer>
            }
            code={`<SwipeContainer
  onSwipeLeft={() => console.log('Swiped left')}
  onSwipeRight={() => console.log('Swiped right')}
  onSwipeUp={() => console.log('Swiped up')}
  onSwipeDown={() => console.log('Swiped down')}
  threshold={50}
  disabled={false}
  showFeedback={true}
>
  <div>Swipe me</div>
</SwipeContainer>`}
            props={[
              {
                name: 'onSwipeLeft',
                type: '() => void',
                description: 'Function called when swiped left',
              },
              {
                name: 'onSwipeRight',
                type: '() => void',
                description: 'Function called when swiped right',
              },
              {
                name: 'onSwipeUp',
                type: '() => void',
                description: 'Function called when swiped up',
              },
              {
                name: 'onSwipeDown',
                type: '() => void',
                description: 'Function called when swiped down',
              },
              {
                name: 'threshold',
                type: 'number',
                default: '50',
                description: 'Minimum distance required to trigger swipe (in pixels)',
              },
              {
                name: 'disabled',
                type: 'boolean',
                default: 'false',
                description: 'Whether to disable swipe gestures',
              },
              {
                name: 'showFeedback',
                type: 'boolean',
                default: 'true',
                description: 'Whether to show visual feedback',
              },
            ]}
            accessibility="This component provides visual feedback for swipe gestures, which helps users understand the interaction. It also supports keyboard navigation for accessibility."
          />
          
          {/* MobileTabs */}
          <ComponentDoc
            name="MobileTabs"
            description="A mobile-friendly tab interface with swipe navigation."
            example={
              <div className="w-full max-w-md">
                <MobileTabs
                  tabs={tabItems}
                  defaultTab="home"
                  enableSwipe={true}
                  showArrows={true}
                  showPagination={true}
                />
              </div>
            }
            code={`<MobileTabs
  tabs={[
    {
      id: 'home',
      label: 'Home',
      icon: <HomeIcon />,
      content: <div>Home content</div>,
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <SettingsIcon />,
      content: <div>Settings content</div>,
    },
  ]}
  defaultTab="home"
  enableSwipe={true}
  showArrows={true}
  showPagination={true}
  onTabChange={(tabId) => console.log('Tab changed:', tabId)}
/>`}
            props={[
              {
                name: 'tabs',
                type: 'Array<{ id: string; label: string; icon?: React.ReactNode; content: React.ReactNode; disabled?: boolean; }>',
                description: 'Tab items',
                required: true,
              },
              {
                name: 'defaultTab',
                type: 'string',
                description: 'Default selected tab ID',
              },
              {
                name: 'enableSwipe',
                type: 'boolean',
                default: 'true',
                description: 'Whether to enable swipe navigation',
              },
              {
                name: 'showArrows',
                type: 'boolean',
                default: 'true',
                description: 'Whether to show navigation arrows',
              },
              {
                name: 'showPagination',
                type: 'boolean',
                default: 'true',
                description: 'Whether to show tab indicators',
              },
              {
                name: 'onTabChange',
                type: '(tabId: string) => void',
                description: 'Function called when tab changes',
              },
            ]}
            accessibility="This component follows the WAI-ARIA Tabs pattern. It provides keyboard navigation, proper ARIA roles, and visual indicators for the selected tab."
          />
          
          {/* More components would be documented here */}
          
          {/* Bottom sheet example */}
          <Card>
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold mb-4">Interactive Examples</h2>
              <div className="flex flex-wrap gap-4">
                <Button onClick={() => setIsBottomSheetOpen(true)}>
                  Open Bottom Sheet
                </Button>
                <Button onClick={() => setIsActionSheetOpen(true)}>
                  Open Action Sheet
                </Button>
                <Button 
                  onClick={() => 
                    showToast({ 
                      type: 'success', 
                      title: 'Success', 
                      message: 'This is a success toast notification',
                      duration: 3000,
                    })
                  }
                >
                  Show Toast
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      
      {/* Bottom Sheet */}
      <BottomSheet
        isOpen={isBottomSheetOpen}
        onClose={() => setIsBottomSheetOpen(false)}
        title="Bottom Sheet"
        showCloseButton={true}
        showDragHandle={true}
      >
        <div className="p-4">
          <p className="mb-4">This is a bottom sheet component that follows iOS 19 design principles.</p>
          <Button onClick={() => setIsBottomSheetOpen(false)}>Close</Button>
        </div>
      </BottomSheet>
      
      {/* Action Sheet */}
      <ActionSheet
        isOpen={isActionSheetOpen}
        onClose={() => setIsActionSheetOpen(false)}
        title="Action Sheet"
        items={actionSheetItems}
        showCancel={true}
        cancelLabel="Cancel"
      />
    </PageLayout>
  );
}
