'use client';

/**
 * Documentation Index Page
 * 
 * This page provides an index of all documentation pages.
 */

import React from 'react';
import { PageLayout } from '@/components/layout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { 
  Smartphone, 
  Layout, 
  Zap, 
  Accessibility, 
  ChevronRight,
  Palette,
  Component,
  Layers,
  FileCode,
  BookOpen
} from 'lucide-react';

/**
 * Documentation Index Page
 */
export default function DocsIndexPage() {
  const docCategories = [
    {
      title: 'Mobile Components',
      description: 'Mobile-friendly components following iOS 19 design principles',
      icon: <Smartphone className="h-6 w-6" />,
      href: '/docs/mobile-components',
      color: 'bg-blue-500/10 text-blue-500',
    },
    {
      title: 'Responsive Layout',
      description: 'Components for creating responsive layouts that work on all screen sizes',
      icon: <Layout className="h-6 w-6" />,
      href: '/docs/responsive-layout',
      color: 'bg-purple-500/10 text-purple-500',
    },
    {
      title: 'Performance Optimization',
      description: 'Components and hooks for optimizing performance on mobile devices',
      icon: <Zap className="h-6 w-6" />,
      href: '/docs/performance',
      color: 'bg-amber-500/10 text-amber-500',
    },
    {
      title: 'Accessibility',
      description: 'Components and best practices for improving accessibility',
      icon: <Accessibility className="h-6 w-6" />,
      href: '/docs/accessibility',
      color: 'bg-green-500/10 text-green-500',
    },
    {
      title: 'UI Components',
      description: 'Core UI components for building interfaces',
      icon: <Component className="h-6 w-6" />,
      href: '/docs/ui-components',
      color: 'bg-indigo-500/10 text-indigo-500',
    },
    {
      title: 'Design System',
      description: 'Design tokens, colors, typography, and spacing',
      icon: <Palette className="h-6 w-6" />,
      href: '/docs/design-system',
      color: 'bg-pink-500/10 text-pink-500',
    },
  ];
  
  return (
    <PageLayout
      title="Documentation"
      description="Documentation for the Weather Route application"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <div className="space-y-4">
          <h1 className="text-4xl font-bold">Documentation</h1>
          <p className="text-xl text-muted-foreground">
            Comprehensive documentation for the Weather Route application components and design system.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {docCategories.map((category) => (
            <Card key={category.title} className="overflow-hidden">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`p-2 rounded-lg ${category.color}`}>
                    {category.icon}
                  </div>
                  <CardTitle>{category.title}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-base">
                  {category.description}
                </CardDescription>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" className="w-full justify-between">
                  <Link href={category.href}>
                    <span>View Documentation</span>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
        
        <div className="mt-12 space-y-6">
          <h2 className="text-2xl font-bold">Getting Started</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-blue-500/10 text-blue-500">
                    <Layers className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-lg">Component Structure</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Learn about the component hierarchy and how components are organized.
                </p>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" size="sm" className="w-full justify-between">
                  <Link href="/docs/getting-started/structure">
                    <span>View Guide</span>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-green-500/10 text-green-500">
                    <FileCode className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-lg">Code Conventions</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Understand the coding standards and best practices used in the project.
                </p>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" size="sm" className="w-full justify-between">
                  <Link href="/docs/getting-started/conventions">
                    <span>View Guide</span>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-amber-500/10 text-amber-500">
                    <BookOpen className="h-5 w-5" />
                  </div>
                  <CardTitle className="text-lg">Usage Examples</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  See examples of how to use the components in different scenarios.
                </p>
              </CardContent>
              <CardFooter>
                <Button asChild variant="ghost" size="sm" className="w-full justify-between">
                  <Link href="/docs/getting-started/examples">
                    <span>View Examples</span>
                    <ChevronRight className="h-4 w-4" />
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </PageLayout>
  );
}
