'use client';

/**
 * Performance Optimization Documentation Page
 * 
 * This page provides documentation for performance optimization components and hooks.
 */

import React, { useState, useEffect } from 'react';
import { ComponentDoc } from '@/components/docs/ComponentDoc';
import { PageLayout } from '@/components/layout';
import { LazyImage, LazyComponent, VirtualList } from '@/components/ui/lazy-load';
import { useDevicePerformance, useDebounce, useThrottle } from '@/hooks/use-performance';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

/**
 * Performance Optimization Documentation Page
 */
export default function PerformanceOptimizationPage() {
  const [count, setCount] = useState(0);
  const debouncedCount = useDebounce(count, 500);
  const throttledCount = useThrottle(count, 300);
  const devicePerformance = useDevicePerformance();
  
  // Generate a list of items for the VirtualList example
  const items = Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    name: `Item ${i}`,
    description: `This is item ${i}`,
  }));
  
  return (
    <PageLayout
      title="Performance Optimization"
      description="Documentation for performance optimization components and hooks"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <h1 className="text-3xl font-bold">Performance Optimization</h1>
        <p className="text-lg text-muted-foreground">
          These components and hooks are designed to optimize performance, especially on mobile devices.
        </p>
        
        <div className="grid grid-cols-1 gap-8">
          {/* LazyImage */}
          <ComponentDoc
            name="LazyImage"
            description="A component that lazily loads images when they enter the viewport."
            example={
              <div className="w-full max-w-md mx-auto">
                <div className="space-y-4">
                  <LazyImage
                    src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4"
                    alt="Mountain landscape"
                    width={400}
                    height={300}
                    className="rounded-lg"
                  />
                  <LazyImage
                    src="https://images.unsplash.com/photo-1454496522488-7a8e488e8606"
                    alt="Mountain range"
                    width={400}
                    height={300}
                    className="rounded-lg"
                    loadingComponent={
                      <div className="flex items-center justify-center h-full">
                        <Loader2 className="h-8 w-8 animate-spin text-primary" />
                      </div>
                    }
                  />
                </div>
              </div>
            }
            code={`<LazyImage
  src="https://example.com/image.jpg"
  alt="Example image"
  width={400}
  height={300}
  className="rounded-lg"
  loadingComponent={<Loader />}
  threshold={0.1}
  onLoad={() => console.log('Image loaded')}
  onError={(error) => console.error('Image error:', error)}
/>`}
            props={[
              {
                name: 'src',
                type: 'string',
                description: 'Image source URL',
                required: true,
              },
              {
                name: 'alt',
                type: 'string',
                description: 'Image alt text',
                required: true,
              },
              {
                name: 'width',
                type: 'number',
                description: 'Image width',
              },
              {
                name: 'height',
                type: 'number',
                description: 'Image height',
              },
              {
                name: 'loadingComponent',
                type: 'React.ReactNode',
                description: 'Custom loading component',
              },
              {
                name: 'threshold',
                type: 'number',
                default: '0.1',
                description: 'Intersection observer threshold',
              },
              {
                name: 'placeholderSrc',
                type: 'string',
                description: 'Placeholder image source',
              },
              {
                name: 'onLoad',
                type: '() => void',
                description: 'Function called when the image is loaded',
              },
              {
                name: 'onError',
                type: '(error: Error) => void',
                description: 'Function called when the image fails to load',
              },
            ]}
            accessibility="This component provides proper alt text for images and shows a loading indicator while the image is loading. It also handles error states gracefully."
          />
          
          {/* VirtualList */}
          <ComponentDoc
            name="VirtualList"
            description="A component that renders only the items that are visible in the viewport."
            example={
              <div className="w-full max-w-md mx-auto h-64 border border-border/20 rounded-lg">
                <VirtualList
                  items={items}
                  renderItem={(item) => (
                    <div className="p-4 border-b border-border/10 last:border-b-0">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm text-muted-foreground">{item.description}</div>
                    </div>
                  )}
                  itemHeight={72}
                  overscan={5}
                />
              </div>
            }
            code={`<VirtualList
  items={items}
  renderItem={(item, index) => (
    <div key={index}>
      <div>{item.name}</div>
      <div>{item.description}</div>
    </div>
  )}
  itemHeight={72}
  overscan={5}
  loadingComponent={<Loader />}
/>`}
            props={[
              {
                name: 'items',
                type: 'T[]',
                description: 'Array of items to render',
                required: true,
              },
              {
                name: 'renderItem',
                type: '(item: T, index: number) => React.ReactNode',
                description: 'Function to render an item',
                required: true,
              },
              {
                name: 'itemHeight',
                type: 'number',
                default: '50',
                description: 'Height of each item in pixels',
              },
              {
                name: 'overscan',
                type: 'number',
                default: '5',
                description: 'Number of items to render outside the visible area',
              },
              {
                name: 'loadingComponent',
                type: 'React.ReactNode',
                description: 'Custom loading component',
              },
            ]}
            design="This component uses virtualization to render only the items that are visible in the viewport, which significantly improves performance for long lists."
          />
          
          {/* Performance Hooks */}
          <Card>
            <CardHeader>
              <CardTitle>Performance Hooks</CardTitle>
              <CardDescription>
                These hooks help optimize performance by reducing unnecessary renders and adapting to device capabilities.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* useDevicePerformance */}
              <div>
                <h3 className="text-lg font-semibold mb-2">useDevicePerformance</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Detects device performance capabilities and returns optimization settings.
                </p>
                <div className="bg-muted/20 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Your Device Performance:</h4>
                  <ul className="text-sm space-y-1">
                    <li>Low-end device: {devicePerformance.isLowEndDevice ? 'Yes' : 'No'}</li>
                    <li>Low memory: {devicePerformance.isLowMemory ? 'Yes' : 'No'}</li>
                    <li>Data saver: {devicePerformance.isDataSaverEnabled ? 'Yes' : 'No'}</li>
                    <li>Reduced motion: {devicePerformance.isReducedMotion ? 'Yes' : 'No'}</li>
                    <li>Connection: {devicePerformance.connectionType}</li>
                    <li>Effective connection: {devicePerformance.effectiveConnectionType}</li>
                  </ul>
                </div>
                <pre className="mt-4 p-4 rounded-lg bg-muted/20 overflow-auto text-xs">
                  <code>{`const devicePerformance = useDevicePerformance();

// Conditionally render based on device capabilities
if (devicePerformance.shouldReduceAnimations) {
  // Use simpler animations or disable them
}

if (devicePerformance.shouldReduceImageQuality) {
  // Use lower quality images
}

if (devicePerformance.shouldUseSimpleLayout) {
  // Use a simpler layout
}`}</code>
                </pre>
              </div>
              
              {/* useDebounce and useThrottle */}
              <div>
                <h3 className="text-lg font-semibold mb-2">useDebounce & useThrottle</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  These hooks help reduce the number of renders or API calls by debouncing or throttling values.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1 p-4 border border-border/20 rounded-lg">
                    <h4 className="font-medium mb-2">Counter Example:</h4>
                    <div className="flex items-center gap-4 mb-4">
                      <Button onClick={() => setCount(count + 1)}>Increment</Button>
                      <span>Count: {count}</span>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div>Raw count: {count}</div>
                      <div>Debounced count: {debouncedCount}</div>
                      <div>Throttled count: {throttledCount}</div>
                    </div>
                  </div>
                  <div className="flex-1">
                    <pre className="p-4 rounded-lg bg-muted/20 overflow-auto text-xs h-full">
                      <code>{`// Debounce example
const [value, setValue] = useState('');
const debouncedValue = useDebounce(value, 500);

// Only make API call when debouncedValue changes
useEffect(() => {
  fetchData(debouncedValue);
}, [debouncedValue]);

// Throttle example
const [scrollPosition, setScrollPosition] = useState(0);
const throttledPosition = useThrottle(scrollPosition, 300);

// Only update UI when throttledPosition changes
useEffect(() => {
  updateUI(throttledPosition);
}, [throttledPosition]);`}</code>
                    </pre>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
}
