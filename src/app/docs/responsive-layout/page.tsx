'use client';

/**
 * Responsive Layout Documentation Page
 * 
 * This page provides documentation for responsive layout components.
 */

import React from 'react';
import { ComponentDoc } from '@/components/docs/ComponentDoc';
import { PageLayout } from '@/components/layout';
import { 
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveSection,
  CardGrid,
  MobileNavigation,
  BottomNavigation,
  ResponsiveLayoutWrapper
} from '@/components/layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Home, Settings, User, Bell, MessageSquare } from 'lucide-react';

/**
 * Responsive Layout Documentation Page
 */
export default function ResponsiveLayoutPage() {
  // Example data for components
  const cardItems = [
    {
      id: 'card1',
      title: 'Card 1',
      description: 'This is card 1',
      content: <div className="h-32 flex items-center justify-center">Card 1 Content</div>,
    },
    {
      id: 'card2',
      title: 'Card 2',
      description: 'This is card 2',
      content: <div className="h-32 flex items-center justify-center">Card 2 Content</div>,
    },
    {
      id: 'card3',
      title: 'Card 3',
      description: 'This is card 3',
      content: <div className="h-32 flex items-center justify-center">Card 3 Content</div>,
    },
    {
      id: 'card4',
      title: 'Card 4',
      description: 'This is card 4',
      content: <div className="h-32 flex items-center justify-center">Card 4 Content</div>,
    },
  ];
  
  const navItems = [
    {
      href: '#',
      label: 'Home',
      icon: <Home className="h-4 w-4" />,
    },
    {
      href: '#',
      label: 'Messages',
      icon: <MessageSquare className="h-4 w-4" />,
    },
    {
      href: '#',
      label: 'Notifications',
      icon: <Bell className="h-4 w-4" />,
    },
    {
      href: '#',
      label: 'Profile',
      icon: <User className="h-4 w-4" />,
    },
    {
      href: '#',
      label: 'Settings',
      icon: <Settings className="h-4 w-4" />,
    },
  ];
  
  return (
    <PageLayout
      title="Responsive Layout"
      description="Documentation for responsive layout components"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <h1 className="text-3xl font-bold">Responsive Layout Components</h1>
        <p className="text-lg text-muted-foreground">
          These components are designed to create responsive layouts that work well on all screen sizes.
        </p>
        
        <div className="grid grid-cols-1 gap-8">
          {/* ResponsiveContainer */}
          <ComponentDoc
            name="ResponsiveContainer"
            description="A responsive container that provides consistent sizing and spacing."
            example={
              <div className="w-full bg-muted/20 p-4">
                <ResponsiveContainer
                  maxWidth="md"
                  padding="md"
                  glass={true}
                  bordered={true}
                  shadowed={true}
                  rounded={true}
                  className="bg-card"
                >
                  <div className="h-32 flex items-center justify-center">
                    <p className="text-center">This is a responsive container</p>
                  </div>
                </ResponsiveContainer>
              </div>
            }
            code={`<ResponsiveContainer
  maxWidth="md"
  padding="md"
  centered={true}
  glass={true}
  bordered={true}
  shadowed={true}
  rounded={true}
>
  <div>Container content</div>
</ResponsiveContainer>`}
            props={[
              {
                name: 'maxWidth',
                type: "'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full' | 'none'",
                default: "'xl'",
                description: 'Container max width',
              },
              {
                name: 'padding',
                type: "'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl'",
                default: "'md'",
                description: 'Container padding',
              },
              {
                name: 'centered',
                type: 'boolean',
                default: 'true',
                description: 'Whether to center the container',
              },
              {
                name: 'glass',
                type: 'boolean',
                default: 'false',
                description: 'Whether to use glass effect',
              },
              {
                name: 'bordered',
                type: 'boolean',
                default: 'false',
                description: 'Whether to show a border',
              },
              {
                name: 'shadowed',
                type: 'boolean',
                default: 'false',
                description: 'Whether to show a shadow',
              },
              {
                name: 'rounded',
                type: 'boolean',
                default: 'false',
                description: 'Whether to show rounded corners',
              },
              {
                name: 'fullHeight',
                type: 'boolean',
                default: 'false',
                description: 'Whether to use full height',
              },
            ]}
            design="This component follows iOS 19 design principles with clean lines, subtle shadows, and rounded corners. It provides a consistent container for content with responsive sizing."
          />
          
          {/* ResponsiveGrid */}
          <ComponentDoc
            name="ResponsiveGrid"
            description="A responsive grid layout that adapts to different screen sizes."
            example={
              <div className="w-full">
                <ResponsiveGrid
                  mobileColumns={1}
                  tabletColumns={2}
                  desktopColumns={3}
                  largeDesktopColumns={4}
                  gap="md"
                >
                  {[1, 2, 3, 4, 5, 6].map((item) => (
                    <div
                      key={item}
                      className="bg-muted/30 border border-border/20 rounded-lg p-4 h-24 flex items-center justify-center"
                    >
                      Item {item}
                    </div>
                  ))}
                </ResponsiveGrid>
              </div>
            }
            code={`<ResponsiveGrid
  mobileColumns={1}
  tabletColumns={2}
  desktopColumns={3}
  largeDesktopColumns={4}
  gap="md"
>
  <div>Item 1</div>
  <div>Item 2</div>
  <div>Item 3</div>
  <div>Item 4</div>
</ResponsiveGrid>`}
            props={[
              {
                name: 'mobileColumns',
                type: '1 | 2',
                default: '1',
                description: 'Number of columns on mobile',
              },
              {
                name: 'tabletColumns',
                type: '1 | 2 | 3',
                default: '2',
                description: 'Number of columns on tablet',
              },
              {
                name: 'desktopColumns',
                type: '1 | 2 | 3 | 4',
                default: '3',
                description: 'Number of columns on desktop',
              },
              {
                name: 'largeDesktopColumns',
                type: '1 | 2 | 3 | 4 | 5 | 6',
                default: '4',
                description: 'Number of columns on large desktop',
              },
              {
                name: 'gap',
                type: "'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl'",
                default: "'md'",
                description: 'Gap between items',
              },
              {
                name: 'autoFit',
                type: 'boolean',
                default: 'false',
                description: 'Whether to use auto-fit instead of fixed columns',
              },
              {
                name: 'minItemWidth',
                type: 'string',
                default: "'250px'",
                description: 'Minimum width for auto-fit columns',
              },
            ]}
            design="This component provides a responsive grid layout that adapts to different screen sizes. It uses CSS Grid to create a flexible and responsive layout."
          />
          
          {/* CardGrid */}
          <ComponentDoc
            name="CardGrid"
            description="A responsive grid of cards that adapts to different screen sizes."
            example={
              <div className="w-full">
                <CardGrid
                  items={cardItems}
                  mobileColumns={1}
                  tabletColumns={2}
                  desktopColumns={2}
                  largeDesktopColumns={4}
                  gap="md"
                  glass={true}
                  bordered={true}
                  shadowed={true}
                  rounded={true}
                  equalHeight={true}
                />
              </div>
            }
            code={`<CardGrid
  items={[
    {
      id: 'card1',
      title: 'Card 1',
      description: 'This is card 1',
      content: <div>Card 1 Content</div>,
    },
    {
      id: 'card2',
      title: 'Card 2',
      description: 'This is card 2',
      content: <div>Card 2 Content</div>,
    },
  ]}
  mobileColumns={1}
  tabletColumns={2}
  desktopColumns={3}
  largeDesktopColumns={4}
  gap="md"
  glass={true}
  bordered={true}
  shadowed={true}
  rounded={true}
  equalHeight={true}
/>`}
            props={[
              {
                name: 'items',
                type: 'Array<{ id: string; title: string; description?: string; content: React.ReactNode; footer?: React.ReactNode; className?: string; }>',
                description: 'Array of card items',
                required: true,
              },
              {
                name: 'mobileColumns',
                type: '1 | 2',
                default: '1',
                description: 'Number of columns on mobile',
              },
              {
                name: 'tabletColumns',
                type: '1 | 2 | 3',
                default: '2',
                description: 'Number of columns on tablet',
              },
              {
                name: 'desktopColumns',
                type: '1 | 2 | 3 | 4',
                default: '3',
                description: 'Number of columns on desktop',
              },
              {
                name: 'largeDesktopColumns',
                type: '1 | 2 | 3 | 4 | 5 | 6',
                default: '4',
                description: 'Number of columns on large desktop',
              },
              {
                name: 'gap',
                type: "'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl'",
                default: "'md'",
                description: 'Gap between items',
              },
              {
                name: 'glass',
                type: 'boolean',
                default: 'true',
                description: 'Whether to use glass effect',
              },
              {
                name: 'bordered',
                type: 'boolean',
                default: 'true',
                description: 'Whether to show a border',
              },
              {
                name: 'shadowed',
                type: 'boolean',
                default: 'true',
                description: 'Whether to show a shadow',
              },
              {
                name: 'rounded',
                type: 'boolean',
                default: 'true',
                description: 'Whether to show rounded corners',
              },
              {
                name: 'equalHeight',
                type: 'boolean',
                default: 'true',
                description: 'Whether to use equal height cards',
              },
            ]}
            design="This component combines the ResponsiveGrid with Card components to create a responsive grid of cards. It follows iOS 19 design principles with clean lines, subtle shadows, and rounded corners."
          />
          
          {/* Navigation Components */}
          <Card>
            <CardHeader>
              <CardTitle>Navigation Components</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold mb-2">MobileNavigation</h3>
                <div className="border border-border/20 rounded-lg overflow-hidden">
                  <MobileNavigation
                    title="Weather Route"
                    navItems={navItems}
                    bordered={true}
                    shadowed={true}
                    glass={true}
                    sticky={false}
                  />
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  A mobile-friendly top navigation bar that follows iOS 19 design principles.
                </p>
              </div>
              
              <div>
                <h3 className="text-lg font-semibold mb-2">BottomNavigation</h3>
                <div className="border border-border/20 rounded-lg overflow-hidden p-4">
                  <div className="relative h-16">
                    <BottomNavigation
                      navItems={navItems}
                      bordered={true}
                      shadowed={true}
                      glass={true}
                      sticky={false}
                      showLabels={true}
                      className="absolute bottom-0 left-0 right-0"
                    />
                  </div>
                </div>
                <p className="text-sm text-muted-foreground mt-2">
                  A mobile-friendly bottom navigation bar that follows iOS 19 design principles.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
}
