'use client';

/**
 * Accessibility Documentation Page
 * 
 * This page provides documentation for accessibility components and best practices.
 */

import React, { useState } from 'react';
import { ComponentDoc } from '@/components/docs/ComponentDoc';
import { PageLayout } from '@/components/layout';
import { 
  SkipLink, 
  VisuallyHidden, 
  FocusTrap, 
  LiveRegion, 
  AccessibleIcon,
  useAnnounce
} from '@/components/ui/accessibility';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Bell, 
  Info, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Home,
  Settings,
  User,
  Search
} from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

/**
 * Accessibility Documentation Page
 */
export default function AccessibilityPage() {
  const [message, setMessage] = useState('');
  const { announce, Announcer } = useAnnounce();
  
  return (
    <PageLayout
      title="Accessibility"
      description="Documentation for accessibility components and best practices"
      maxWidth="xl"
      padding="md"
    >
      <div className="space-y-8">
        <h1 className="text-3xl font-bold">Accessibility Components</h1>
        <p className="text-lg text-muted-foreground">
          These components help improve accessibility following WCAG 2.1 AA standards.
        </p>
        
        <div className="grid grid-cols-1 gap-8">
          {/* SkipLink */}
          <ComponentDoc
            name="SkipLink"
            description="A skip link that allows keyboard users to bypass navigation and go directly to the main content."
            example={
              <div className="border border-border/20 rounded-lg p-4">
                <p className="text-sm text-muted-foreground mb-4">
                  Tab into this container to see the skip link. It will only be visible when focused.
                </p>
                <div className="relative border border-border/20 rounded-lg p-4">
                  <SkipLink href="#main-content">Skip to main content</SkipLink>
                  <div className="flex flex-col gap-2">
                    <Button variant="outline">Navigation Item 1</Button>
                    <Button variant="outline">Navigation Item 2</Button>
                    <Button variant="outline">Navigation Item 3</Button>
                  </div>
                  <div id="main-content" className="mt-4 p-4 bg-muted/20 rounded-lg">
                    <h3 className="font-medium mb-2">Main Content</h3>
                    <p className="text-sm text-muted-foreground">
                      This is the main content area that users can skip to.
                    </p>
                  </div>
                </div>
              </div>
            }
            code={`<SkipLink href="#main-content">Skip to main content</SkipLink>

<nav>
  {/* Navigation items */}
</nav>

<main id="main-content">
  {/* Main content */}
</main>`}
            props={[
              {
                name: 'href',
                type: 'string',
                default: "'#main-content'",
                description: 'The ID of the element to skip to',
              },
              {
                name: 'children',
                type: 'React.ReactNode',
                default: "'Skip to main content'",
                description: 'The text of the skip link',
              },
            ]}
            accessibility="Skip links are essential for keyboard users to bypass repetitive navigation. They should be the first focusable element on the page and should only be visible when focused."
          />
          
          {/* VisuallyHidden */}
          <ComponentDoc
            name="VisuallyHidden"
            description="A component that hides content visually but keeps it accessible to screen readers."
            example={
              <div className="border border-border/20 rounded-lg p-4">
                <Button>
                  <Bell className="h-4 w-4 mr-2" />
                  Notifications
                  <VisuallyHidden>(5 unread)</VisuallyHidden>
                </Button>
                <p className="text-sm text-muted-foreground mt-4">
                  The text "(5 unread)" is visually hidden but will be announced by screen readers.
                </p>
              </div>
            }
            code={`<Button>
  <Bell className="h-4 w-4 mr-2" />
  Notifications
  <VisuallyHidden>(5 unread)</VisuallyHidden>
</Button>`}
            props={[
              {
                name: 'children',
                type: 'React.ReactNode',
                description: 'The content to hide visually',
                required: true,
              },
              {
                name: 'asChild',
                type: 'boolean',
                default: 'false',
                description: 'Whether to render as a child component',
              },
            ]}
            accessibility="This component is useful for providing additional context to screen reader users without cluttering the visual interface. It uses CSS to hide content visually while keeping it accessible to screen readers."
          />
          
          {/* AccessibleIcon */}
          <ComponentDoc
            name="AccessibleIcon"
            description="A component that wraps an icon with a visually hidden label for screen readers."
            example={
              <div className="border border-border/20 rounded-lg p-4">
                <div className="flex gap-4">
                  <Button variant="ghost" size="icon">
                    <AccessibleIcon label="Home">
                      <Home className="h-5 w-5" />
                    </AccessibleIcon>
                  </Button>
                  <Button variant="ghost" size="icon">
                    <AccessibleIcon label="Settings">
                      <Settings className="h-5 w-5" />
                    </AccessibleIcon>
                  </Button>
                  <Button variant="ghost" size="icon">
                    <AccessibleIcon label="User Profile">
                      <User className="h-5 w-5" />
                    </AccessibleIcon>
                  </Button>
                  <Button variant="ghost" size="icon">
                    <AccessibleIcon label="Search">
                      <Search className="h-5 w-5" />
                    </AccessibleIcon>
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-4">
                  Each icon has a visually hidden label that will be announced by screen readers.
                </p>
              </div>
            }
            code={`<Button variant="ghost" size="icon">
  <AccessibleIcon label="Home">
    <HomeIcon className="h-5 w-5" />
  </AccessibleIcon>
</Button>`}
            props={[
              {
                name: 'label',
                type: 'string',
                description: 'The accessible label for the icon',
                required: true,
              },
              {
                name: 'children',
                type: 'React.ReactNode',
                description: 'The icon to wrap',
                required: true,
              },
            ]}
            accessibility="Icons without text labels need accessible names for screen reader users. This component wraps an icon with a visually hidden label and sets the appropriate ARIA attributes."
          />
          
          {/* FocusTrap */}
          <ComponentDoc
            name="FocusTrap"
            description="A component that traps focus within a container, useful for modals and dialogs."
            example={
              <div className="border border-border/20 rounded-lg p-4">
                <Dialog>
                  <DialogTrigger asChild>
                    <Button>Open Dialog with Focus Trap</Button>
                  </DialogTrigger>
                  <DialogContent>
                    <FocusTrap>
                      <DialogHeader>
                        <DialogTitle>Focus is trapped within this dialog</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <p className="text-sm text-muted-foreground">
                          Try tabbing through the elements in this dialog. Focus will be trapped within it.
                        </p>
                        <div className="flex flex-col gap-2">
                          <Button>Button 1</Button>
                          <Button>Button 2</Button>
                          <Button>Button 3</Button>
                        </div>
                      </div>
                    </FocusTrap>
                  </DialogContent>
                </Dialog>
              </div>
            }
            code={`<Dialog>
  <DialogTrigger>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <FocusTrap>
      <DialogHeader>
        <DialogTitle>Focus is trapped within this dialog</DialogTitle>
      </DialogHeader>
      <div className="space-y-4">
        <p>Dialog content</p>
        <Button>Button 1</Button>
        <Button>Button 2</Button>
      </div>
    </FocusTrap>
  </DialogContent>
</Dialog>`}
            props={[
              {
                name: 'children',
                type: 'React.ReactNode',
                description: 'The content to trap focus within',
                required: true,
              },
              {
                name: 'active',
                type: 'boolean',
                default: 'true',
                description: 'Whether the focus trap is active',
              },
            ]}
            accessibility="Focus trapping is essential for modal dialogs to ensure keyboard users can't accidentally interact with content outside the dialog. This component traps focus within its children and cycles focus back to the first focusable element when the user tabs past the last one."
          />
          
          {/* LiveRegion and useAnnounce */}
          <ComponentDoc
            name="LiveRegion & useAnnounce"
            description="Components for announcing dynamic content changes to screen readers."
            example={
              <div className="border border-border/20 rounded-lg p-4">
                <div className="space-y-4">
                  <div className="flex flex-wrap gap-2">
                    <Button 
                      variant="outline" 
                      onClick={() => announce('This is a polite announcement', 'polite')}
                    >
                      <Info className="h-4 w-4 mr-2" />
                      Polite Announcement
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => announce('This is an assertive announcement', 'assertive')}
                    >
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Assertive Announcement
                    </Button>
                  </div>
                  <div className="flex flex-col gap-2">
                    <div className="flex items-center gap-2">
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="h-8 px-2"
                        onClick={() => {
                          setMessage('Success! Your changes have been saved.');
                          announce('Success! Your changes have been saved.', 'polite');
                        }}
                      >
                        <CheckCircle className="h-4 w-4 text-green-500" />
                        <span className="sr-only">Show success message</span>
                      </Button>
                      <Button 
                        size="sm" 
                        variant="ghost" 
                        className="h-8 px-2"
                        onClick={() => {
                          setMessage('Error! Something went wrong.');
                          announce('Error! Something went wrong.', 'assertive');
                        }}
                      >
                        <XCircle className="h-4 w-4 text-red-500" />
                        <span className="sr-only">Show error message</span>
                      </Button>
                      <span className="text-sm">{message}</span>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    These announcements will be read by screen readers but are not visible on the page.
                  </p>
                </div>
                <Announcer />
              </div>
            }
            code={`// Using LiveRegion directly
<LiveRegion ariaLive="polite">
  {message}
</LiveRegion>

// Using useAnnounce hook
const { announce, Announcer } = useAnnounce();

// In your component
<Button onClick={() => announce('Message announced to screen readers', 'polite')}>
  Announce
</Button>

// At the end of your component
<Announcer />`}
            props={[
              {
                name: 'ariaLive',
                type: "'off' | 'polite' | 'assertive'",
                default: "'polite'",
                description: 'The ARIA live region politeness setting',
              },
              {
                name: 'ariaAtomic',
                type: 'boolean',
                default: 'true',
                description: 'Whether the entire region should be announced as a whole',
              },
              {
                name: 'children',
                type: 'React.ReactNode',
                description: 'The content to announce',
                required: true,
              },
            ]}
            accessibility="Live regions are essential for announcing dynamic content changes to screen readers. Use 'polite' for non-urgent updates and 'assertive' for important alerts that need immediate attention."
          />
        </div>
      </div>
    </PageLayout>
  );
}
